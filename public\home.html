<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#3A5A9F">
    <title>DhipyCare</title>
    <link rel="manifest" href="/manifest.json">
    <style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #e9f0ff 0%, #f0f2f5 100%);
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background-color: #3A5A9F;
    border-bottom-left-radius: 25px;
    border-bottom-right-radius: 25px;
    padding: 10px 25px; /* Reduced top/bottom padding from 15px */
}

nav {
    color: white;
    padding: 15px 25px; /* Desktop padding for nav items */
    /* position: sticky; REMOVED */
    /* top: 0; REMOVED */
    /* z-index: 1000; REMOVED */
    /* display: flex, : column, gap: 18px REMOVED */
    /* border-bottom-left-radius: 25px; REMOVED */
    /* border-bottom-right-radius: 25px; REMOVED */
    /* background-color: #3A5A9F; REMOVED */
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px; /* Limits width of nav content */
    margin: 0 auto; /* Centers nav content within the header */
    /* width: 100%; REMOVED - Let nav content determine its own width up to max-width */
}

/* Styles for the header top row wrapper */
.header-top-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%; /* Ensures it takes the full width of the nav container */
}

.user-info {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.user-info span {
    font-size: 1.7em; /* Increased from 1.5em */
    font-weight: 500;
}

.user-info i {
    font-size: 2.4em; /* Increased from 2.2em */
    margin-right: 12px;
}

.location {
    display: flex;
    align-items: center;
    background-color: white;
    color: #333;
    padding: 10px 18px;
    border-radius: 25px;
    font-size: 1em; /* Base size for the location bar */
    cursor: pointer;
}

.location span {
    font-size: 1.35em;
    line-height: 1.3;
    flex-grow: 1; /* Allow span to take available space */
    text-align: center; /* Center text within the span */
    white-space: nowrap; /* Prevent wrapping */
    overflow: hidden; /* Hide overflow */
    text-overflow: ellipsis; /* Add ellipsis for overflow */
    margin: 0 5px; /* Add small margin to prevent touching icons */
}

.location i {
    margin: 0 6px;
}

.location .fa-map-marker-alt {
    color: red;
    font-size: 1.35em; /* Adjusted from 1.3em to balance larger text */
}

.location .fa-chevron-down {
    font-size: 1.05em; /* Adjusted from 1em */
    color: #333;
}

/* Styles for Delivery Time Indicator */
.delivery-time-indicator {
    display: flex;
    flex-direction: column; /* Stack number and unit */
    align-items: center;
    justify-content: center;
    background-color: #D1F2EB; /* New background color */
    color: #3A5A9F; /* Blue text */
    border-radius: 8px; /* Rounded square */
    width: 60px; /* Adjusted width */
    height: 60px; /* Adjusted height */
    /* font-size: 0.9em; Removed, will style children */
    font-weight: bold;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-left: 15px; /* Space from the location or user element on desktop */
    flex-shrink: 0; /* Prevent shrinking */
    padding: 5px; /* Add some padding */
}

.delivery-time-value {
    font-size: 1.8em; /* Increased from 1.6em */
    font-weight: bold;
    line-height: 1.1;
}

.delivery-time-unit {
    font-size: 1.0em; /* Increased from 0.9em */
    line-height: 1;
}

/* New Search Bar Styles */
/* .header-search-container, .search-icon, .header-search-input styles removed */

main {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 20px;
}

/* Quick Actions Section */
.quick-actions {
    display: flex;
    justify-content: space-around;
    gap: 20px;
    margin-bottom: 30px;
}

.action-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    display: flex;
    flex-direction: column; /* Align items vertically initially */
    align-items: center;
    justify-content: space-between; /* Distribute space */
    transition: transform 0.3s ease;
    cursor: pointer;
}

.action-card:hover {
    transform: translateY(-5px);
}

.action-card img {
    width: 60px; /* Adjust as needed */
    height: 60px;
    margin-bottom: 10px;
}

.action-card h3 {
    margin: 10px 0;
    font-size: 1.1em;
    color: #3A5A9F;
}

.action-card i {
    font-size: 1.2em;
    color: #3A5A9F;
    margin-top: auto; /* Push arrow to the bottom */
}

/* Services Section */
.services h2,
.faqs h2 {
    color: #3A5A9F;
    margin-bottom: 20px;
    font-size: 1.8em;
}

.service-item {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
}

.service-item img {
    width: 80px; /* Adjust as needed */
    height: 80px;
    border-radius: 8px;
}

.service-details h3 {
    color: #3A5A9F;
    margin-top: 0;
    margin-bottom: 10px;
}

.service-details ul {
    list-style: disc;
    padding-left: 20px;
    margin: 0;
    font-size: 0.95em;
}

.service-details ul li {
    margin-bottom: 5px;
}

/* FAQs Section */
.faq-item {
    background-color: white;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    position: relative; /* For z-index if needed later */
}

.faq-item.active {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.faq-item p {
    margin: 0;
    font-size: 1.1em;
}

.faq-item i {
    color: #3A5A9F;
    font-size: 1.2em;
    transition: transform 0.3s ease;
}

.faq-item.active i {
    transform: rotate(45deg); /* Optional: nicer animation for plus/minus */
}

.faq-answer {
    display: none;
    padding: 15px 20px;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-top: none;
    border-radius: 0 0 10px 10px;
    margin-top: -10px; /* Pulls it up to connect with the faq-item */
    margin-bottom: 10px;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
    font-size: 0.95em;
    line-height: 1.6;
}

/* Styles for Doctor Listing and Concerns (from docinfo.html) */
.doctor-browse-view-title { /* New class for the title */
    color: #3A5A9F;
    font-size: 1.8em; /* Consistent with other main titles */
    margin-bottom: 20px;
    text-align: center; /* As per screenshot */
}

.doctor-list-section {
    margin-bottom: 30px;
}

.doctor-scroll-container {
    display: flex;
    overflow-x: auto;
    padding-bottom: 15px; /* Space for scrollbar */
    gap: 20px;
}

.doctor-scroll-container::-webkit-scrollbar {
    display: none;
}
.doctor-scroll-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.doctor-card {
    background-color: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    min-width: 200px;
    flex-shrink: 0;
}

.doctor-card img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 10px;
    object-fit: cover;
    background-color: #e0e0e0;
}

.doctor-card h3 {
    margin: 10px 0 5px 0;
    font-size: 1.1em;
    color: #333;
}

.doctor-card p {
    font-size: 0.85em;
    color: #555;
    margin: 0;
}

.concerns-section {
    margin-bottom: 30px;
}

.concerns-section h2 { /* This is the "What are you concerned about?" title */
    color: #3A5A9F;
    font-size: 1.6em; /* Slightly smaller than main titles, as per design */
    margin-bottom: 20px;
    text-align: center;
}

.concerns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 20px;
    justify-items: center;
}

.concern-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    cursor: pointer;
}

.concern-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #e7f0ff;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    font-size: 1.2em;
    color: #3A5A9F;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.concern-item:nth-child(2) .concern-icon { background-color: #ffe7e7; color: #c0392b; }
.concern-item:nth-child(3) .concern-icon { background-color: #e7ffee; color: #27ae60; }
.concern-item:nth-child(4) .concern-icon { background-color: #fff9e7; color: #f39c12; }

/* Styles for the Back button and title in Doctor Browse View */
#doctorBrowseView .account-page-header {
    /* display: flex; align-items: center; is inherited from general .account-page-header */
    margin-bottom: 25px; /* Adjusted for visual balance */
}

#doctorBrowseView .back-btn {
    background: none;
    border: none;
    color: #3A5A9F;
    cursor: pointer;
    display: flex;
    align-items: center;

    font-size: 1rem;    /* Base size for the button, affecting text "Back" */
    padding: 8px 12px;  /* Comfortable padding */
    margin-right: 15px; /* Spacing from title area */
    border-radius: 6px; /* Modern rounded corners */
    transition: background-color 0.2s ease; /* Smooth hover transition */
}

#doctorBrowseView .back-btn:hover {
    background-color: rgba(58, 90, 159, 0.08); /* Subtle hover */
}

#doctorBrowseView .back-btn i {
    font-size: 1.2rem;  /* Icon a bit larger than text */
    margin-right: 6px;  /* Space between icon and text */
    line-height: 1;
}

#doctorBrowseView .back-btn .back-btn-text {
    display: inline; /* Ensure text is visible */
    line-height: 1;
}

#doctorBrowseView .doctor-browse-view-title {
    flex-grow: 1;
    margin-bottom: 0;
    padding-right: 0; /* Ensure no specific padding that would uncenter title */
    /* color, font-size, text-align: center are inherited or already set */
}

/* Responsive Design */
@media (max-width: 768px) {
    header {
        padding: 8px 15px; /* Further reduced padding */
    }
    nav {
        flex-direction: column;
        align-items: center;
        gap: 6px; /* Further reduced gap */
    }

    .user-info span {
        font-size: 1.5em; /* Increased from 1.3em */
    }
    .user-info i {
        font-size: 2.2em; /* Increased from 2.1em */
        margin-right: 10px;
    }

    .delivery-time-indicator {
        width: 52px; /* Slightly adjusted for new font sizes */
        height: 52px;
        margin-left: 8px;
        padding: 4px;
    }
    .delivery-time-value {
        font-size: 1.4em;
    }
    .delivery-time-unit {
        font-size: 0.8em;
    }

    .location {
        width: 100%;
        padding: 8px 12px; /* Slightly reduce padding */
        font-size: 0.95em; /* Slightly reduce font size */
    }
     .location span {
        font-size: 1.2em;
        /* flex-grow, text-align, overflow properties inherited or can be re-stated if needed */
        /* max-width: 120px; Ensure this is removed if present */
    }
    .location i {
        font-size: 1.1em; /* Adjust icon sizes in location bar if needed */
    }
    .location .fa-chevron-down {
        font-size: 0.9em;
    }

    .header-top-row {
        /* It already has display:flex, justify-content:space-between, align-items:center, width:100% */
        /* No changes needed specifically for this breakpoint unless overriding */
    }

    .user-info {
        /* Adjust user-info specific styles for mobile if needed */
        /* e.g., font size for user name or icon size was handled in your previous reverts */
    }

    .quick-actions {
        flex-direction: column;
    }

    .action-card {
        flex-direction: row; /* Change to row for smaller screens */
        justify-content: space-between; /* Adjust justification */
        align-items: center;
        padding: 15px;
    }

    .action-card img {
        margin-bottom: 0;
        margin-right: 15px;
    }

    .action-card h3 {
        text-align: left;
        flex-grow: 1; /* Allow h3 to take available space */
        margin: 0;
    }

    .action-card i {
        margin-top: 0; /* Reset margin for row layout */
    }

    .service-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: center;
    }

    .service-item img {
        margin-bottom: 15px;
        align-self: center;
    }

    .service-details ul {
        text-align: left;
        padding-left: 30px; /* Indent list items */
    }

    /* Responsive for doctors/concerns */
    .doctor-browse-view-title {
        font-size: 1.5em;
    }
    .doctor-card {
        min-width: 180px;
    }
    .concerns-section h2 {
        font-size: 1.4em;
    }
    .concerns-grid {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 15px;
    }
    .concern-icon {
        width: 50px;
        height: 50px;
        font-size: 1em;
    }
    .concern-item p {
        font-size: 0.8em;
    }
    #doctorBrowseView .doctor-browse-view-title {
        padding-right: 0; /* Ensure no specific padding here either */
        /* font-size: 1.5em; is already set for .doctor-browse-view-title in mobile for this section */
    }
}

@media (max-width: 480px) {
    header {
        padding: 6px 10px; /* Minimized padding */
    }
    nav {
        gap: 5px; /* Minimized gap */
    }

    .user-info span {
        font-size: 1.2em; /* Increased from 1em */
    }
    .user-info i {
        font-size: 1.8em; /* Minimized user icon size */
        margin-right: 8px;
    }

    .delivery-time-indicator {
        width: 45px;
        height: 45px;
        margin-left: 5px;
        padding: 3px;
    }
    .delivery-time-value {
        font-size: 1.3em;
    }
    .delivery-time-unit {
        font-size: 0.75em;
    }

    .location {
        padding: 6px 10px;
        font-size: 0.85em;
    }
     .location span {
        max-width: 120px; /* Adjust if necessary */
        font-size: 1.1em; /* Increased from 0.9em */
    }
    /* .user-info span, i styles from your revert for 480px should apply */
    /* .location styles from your revert for 480px should apply */
    /* .location span styles from your revert for 480px should apply */

    .services h2,
    .faqs h2 {
        font-size: 1.5em;
    }

    .action-card h3 {
        font-size: 1em;
    }

    .service-details h3 {
        font-size: 1.1em;
    }

    .faq-item p {
        font-size: 1em;
    }
}

/* Added Styles for Profile Sidebar and Account Page */
.profile-sidebar {
    position: fixed;
    top: 0;
    left: -300px; /* Initially hidden */
    width: 300px;
    height: 100%;
    background-color: #2c3e50; /* Dark blue from image 1 */
    color: white;
    padding-top: 20px;
    box-shadow: 2px 0 5px rgba(0,0,0,0.2);
    transition: left 0.3s ease;
    z-index: 1001; /* Above header */
}

.profile-sidebar.active {
    left: 0;
}

.profile-sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px 20px 20px;
    border-bottom: 1px solid #34495e;
}

.profile-sidebar-header h3 {
    margin: 0;
    font-size: 1.5em;
}

.close-sidebar-btn {
    font-size: 1.5em;
    cursor: pointer;
}

.profile-sidebar-user {
    text-align: center;
    padding: 20px;
    border-bottom: 1px solid #34495e;
}

.profile-sidebar-user i {
    font-size: 4em;
    margin-bottom: 10px;
}

.profile-sidebar-user p {
    margin: 0;
    font-size: 1.2em;
    font-weight: bold;
}

.profile-sidebar nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.profile-sidebar nav ul li a {
    display: block;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    font-size: 1.1em;
    border-bottom: 1px solid #34495e;
    transition: background-color 0.2s ease;
}

.profile-sidebar nav ul li a:hover {
    background-color: #34495e;
}

.account-page {
    display: none; /* Hidden by default */
    padding: 20px;
    background-color: #f0f2f5;
    max-width: 1200px;
    margin: 0 auto; /* Centered like main content */
}

.account-page-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.back-btn {
    background: none;
    border: none;
    font-size: 1.2em;
    color: #3A5A9F;
    cursor: pointer;
    margin-right: 15px;
    display: flex;
    align-items: center;
}

.back-btn i {
    margin-right: 5px;
}

.account-page-header h2 {
    margin: 0;
    color: #3A5A9F;
    font-size: 1.8em;
}

.account-profile-summary {
    text-align: center;
    margin-bottom: 30px;
}

.account-profile-summary i {
    font-size: 6em;
    color: #bdc3c7;
    margin-bottom: 10px;
}

.account-profile-summary h3 {
    margin: 0;
    font-size: 1.5em;
    color: #333;
}

.account-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.detail-item {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-left: 4px solid #3A5A9F;
}

.detail-item label {
    display: block;
    font-size: 0.9em;
    color: #555;
    margin-bottom: 5px;
}

.detail-item p {
    margin: 0;
    font-size: 1.1em;
    font-weight: bold;
}

.edit-profile-btn {
    display: block;
    width: fit-content;
    margin: 0 auto;
    padding: 12px 30px;
    background-color: #3A5A9F;
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 1.1em;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.edit-profile-btn:hover {
    background-color: #2c4a8c;
}

.account-page.edit-mode .detail-item p {
    display: none;
}

.account-page.edit-mode .detail-item input,
.account-page.edit-mode .detail-item select {
    display: block;
    width: calc(100% - 10px); /* Adjust for padding/border */
    padding: 8px;
    margin-top: 4px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1em;
}

.account-page.edit-mode .account-profile-summary h3 {
    display: none;
}
.account-page.edit-mode .account-profile-summary input#accountPageUserNameInput {
    display: block;
    margin: 0 auto 10px auto;
    padding: 8px;
    font-size: 1.5em;
    text-align: center;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.profile-edit-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

/* Custom Logout Confirmation Modal */
.logout-confirm-modal {
    display: none; /* Hidden by default */
    position: fixed;
    z-index: 2000; /* Above everything else */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5); /* Dim background */
    justify-content: center;
    align-items: center;
}

.logout-confirm-modal-content {
    background-color: #fff;
    margin: auto;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    text-align: center;
    width: 90%;
    max-width: 400px;
}

.logout-confirm-modal-content h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

.logout-confirm-buttons button {
    padding: 10px 20px;
    margin: 0 10px;
    border-radius: 5px;
    border: none;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

#confirmLogoutBtn {
    background-color: #e74c3c; /* Red for confirm/danger */
    color: white;
}

#confirmLogoutBtn:hover {
    background-color: #c0392b;
}

#cancelLogoutBtn {
    background-color: #bdc3c7; /* Grey for cancel */
    color: #333;
}

#cancelLogoutBtn:hover {
    background-color: #95a5a6;
}

/* Hide main content when account page is active */
main.hidden {
    display: none;
}

/* Location Selection Modal */
.location-modal {
    display: none; /* Hidden by default, shown by JS */
    position: fixed;
    z-index: 2001; /* Above profile sidebar and logout modal */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.7); /* Darker overlay */
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
}

.location-modal-content {
    background-color: #ffffff;
    padding: 30px 40px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    width: 90%;
    max-width: 550px;
    text-align: center;
    animation: fadeInModal 0.3s ease-out;
}

@keyframes fadeInModal {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.location-modal-content h2 {
    color: #3A5A9F;
    margin-top: 0;
    margin-bottom: 25px;
    font-size: 1.8em;
}

.location-search-input {
    width: calc(100% - 22px); /* Full width minus padding and border */
    padding: 12px 10px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1em;
    box-sizing: border-box;
}

.map-placeholder {
    width: 100%;
    height: 250px;
    min-height: 250px; /* Added for robustness */
    background-color: #e9ecef;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 25px;
    color: #6c757d;
    font-style: italic;
}

.confirm-location-btn {
    background-color: #3A5A9F;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1.1em;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: block;
    width: fit-content;
    margin: 0 auto;
}

.confirm-location-btn:hover {
    background-color: #2c4a8c;
}

.current-location-btn {
    background-color: #1abc9c; /* Teal color */
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.95em;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-bottom: 15px; /* Space between this and map */
    display: block;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
}

.current-location-btn:hover {
    background-color: #16a085;
}

.current-location-btn i {
    margin-right: 8px;
}

/* Ensure button content aligns well when spinner is active */
.current-location-btn .button-content-original,
.current-location-btn .button-content-loading {
    display: inline-flex; /* Aligns icon and text nicely */
    align-items: center;
}

.current-location-btn .button-content-loading i {
    margin-right: 0; /* No margin needed if only spinner is shown */
}

.location-modal-content .close-modal-btn {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 1.8em;
    color: #aaa;
    cursor: pointer;
    line-height: 1;
}

.location-modal-content .close-modal-btn:hover {
    color: #333;
}

#selectedAddress {
    margin-top: 15px;
    margin-bottom: 20px;
    font-size: 0.95em;
    color: #333;
    min-height: 20px; /* Ensure space even if empty */
}

.container {
    max-width: 420px;
    margin: 48px auto 0 auto;
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(58,90,159,0.18), 0 1.5px 8px rgba(58,90,159,0.08);
    border: 1.5px solid #e3e8f7;
    padding: 36px 26px 28px 26px;
    animation: fadeInForm 0.7s cubic-bezier(.4,1.4,.6,1) 1;
}
@keyframes fadeInForm {
    from { opacity: 0; transform: translateY(30px) scale(0.98); }
    to { opacity: 1; transform: none; }
}
.form-title {
    color: #3A5A9F;
    font-size: 1.6em;
    margin-bottom: 22px;
    text-align: center;
    font-weight: 600;
    letter-spacing: 0.01em;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}
.form-title i {
    color: #2c4a8c;
    font-size: 1.1em;
}
.form-group {
    margin-bottom: 20px;
}
label {
    display: block;
    margin-bottom: 7px;
    color: #2d3a5a;
    font-weight: 500;
    letter-spacing: 0.01em;
}
input, select, textarea {
    width: 100%;
    padding: 11px 13px;
    border: 1.5px solid #d0d4e0;
    border-radius: 8px;
    font-size: 1em;
    background: #f9fafd;
    box-sizing: border-box;
    transition: border-color 0.2s, box-shadow 0.2s;
}
input:focus, select:focus, textarea:focus {
    border-color: #3A5A9F;
    outline: none;
    box-shadow: 0 0 0 2px #e7f0ff;
    background: #fff;
}
textarea { resize: vertical; min-height: 48px; }
button[type="submit"] {
    background: linear-gradient(90deg, #3A5A9F 60%, #2c4a8c 100%);
    color: #fff;
    border: none;
    border-radius: 25px;
    padding: 13px 0;
    width: 100%;
    font-size: 1.13em;
    margin-top: 12px;
    font-weight: 600;
    letter-spacing: 0.01em;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(58,90,159,0.08);
    transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
}
button[type="submit"]:hover {
    background: linear-gradient(90deg, #2c4a8c 60%, #3A5A9F 100%);
    box-shadow: 0 4px 16px rgba(58,90,159,0.13);
    transform: translateY(-2px) scale(1.01);
}
.back-btn {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    color: #3A5A9F;
    font-size: 1em;
    margin-bottom: 22px;
    cursor: pointer;
    font-weight: 500;
    padding: 6px 0 6px 0;
    border-radius: 7px;
    transition: background 0.18s, color 0.18s;
}
.back-btn:hover {
    background: #e7f0ff;
    color: #2c4a8c;
}
.back-btn i {
    margin-right: 8px;
    font-size: 1.2em;
}

/* Your Provided CSS for Delivery Promise Box - Modified for Fixed Full-Width Bottom Banner */
.delivery-box {
    position: fixed; /* Added */
    bottom: 0; /* Added */
    left: 0; /* Added */
    width: 100%; /* Added */
    background-color: #adbdc1;
    border-radius: 25px 25px 0 0; /* Kept for top rounded corners */
    padding: 8px 20px; /* Reduced top/bottom padding from 15px */
    display: flex;
    align-items: center;
    justify-content: center; /* Center content for full-width */
    box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.1); /* Shadow on top */
    /* max-width: 400px; Removed for full-width */
    box-sizing: border-box;
    gap: 10px; /* Creates space between icon and text */
    z-index: 1000; /* Ensure it's on top */
}

.delivery-icon-wrapper {
    width: 45px; /* Reduced from 60px */
    height: 45px; /* Reduced from 60px */
    border-radius: 50%;
    background-color: #e0f2f7;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
}

.delivery-icon-img {
    max-width: 80%;
    max-height: 80%;
    display: block;
}

.delivery-text {
    font-size: 19px; /* Increased from 16px */
    font-weight: bold;
    color: #333333;
    margin: 0;
    line-height: 1.2;
    flex-grow: 1;
    text-align: center; /* Changed from left to center */
}

.highlight-red {
    color: #e74c3c;
}

@media (max-width: 480px) {
    .delivery-box {
        /* flex-direction: column; Retain row for full-width banner */
        /* text-align: center; */
        padding: 8px 15px; /* Slightly increased padding */
        gap: 10px; /* Slightly increased gap */
        /* border-radius: 25px 25px 0 0; Already set */
        justify-content: center; /* Center items on mobile as well */
    }
    .delivery-icon-wrapper {
        width: 40px;
        height: 40px;
        /* margin-bottom: 5px; Not needed for row layout */
    }
    .delivery-text {
        font-size: 22px; /* Increased from 17px for mobile */
        text-align: center;
    }
}

    /* Responsive adjustments for .items-container and .consultation-item */
    .items-container {
        gap: 10px; /* Reduce gap for smaller screens */
        justify-content: space-around; /* Better distribution on smaller screens */
    }
    .consultation-item {
        width: calc(50% - 10px); /* Aim for 2 items per row, accounting for gap */
        max-width: 130px; /* Max width for smaller items */
    }
    .item-title {
        font-size: 13px;
    }

    /* Copied and adapted styles from experiment.html */
    .items-container {
        display: flex;
        flex-wrap: wrap;
        gap: 30px; /* Adjusted gap */
        width: 100%; /* Takes full width of its parent in home.html */
        justify-content: center; /* Center items on the line */
        margin-bottom: 30px; /* Add some space below this section */
    }

    .consultation-item {
        display: flex;
        flex-direction: column;
        width: 160px;  /* Increased from 140px */
        cursor: pointer; /* Add cursor pointer to whole item */
        -webkit-tap-highlight-color: transparent; /* Prevent tap highlight on mobile */
    }

    .item-image-box {
        border-radius: 10px;
        overflow: hidden;
        width: 105%;
        height: 100px; /* Increased from 90px */
        position: relative;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .item-image {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
        opacity: 0.1;
    }

    .overlay-icon {
        position: absolute;
        top: 6px;
        right: 6px;
        width: 24px;
        height: 24px;
        background-color: rgba(255, 255, 255, 0.85);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        color: #444;
        box-shadow: 0 1px 2px rgba(0,0,0,0.15);
        /* cursor: pointer; Moved to .consultation-item */
    }

    .item-caption {
        display: flex;
        align-items: center;
        width: 100%;
        margin-top: 8px;
        padding: 0 2px;
        box-sizing: border-box;
        justify-content: center; /* Center caption content */
    }

    .item-title {
        font-size: 14px;
        font-weight: 500;
        color: #333D47;
        line-height: 1.3;
        margin-right: 5px;
        text-align: center; /* Ensure title text is centered */
        flex-grow: 1; /* Allow title to take space */
    }

    .item-arrow {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234A4A4A'%3E%3Cpath d='M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: center;
    }

    .item1 .item-image-box { background-color: #F0F3F9; }
    .item2 .item-image-box { background-color: #E3F2FD; }
    .item3 .item-image-box { background-color: #E0F7FA; }
    .item4 .item-image-box { background-color: #FFF3E0; }
    /* End of copied styles */

    /* CSS for New Carousel Banners */
        .carousel-container {
            width: 90%;
            max-width: 500px; /* Adjusted for a more app-like feel */
            overflow: hidden;
            position: relative;
            border-radius: 15px; /* More rounded corners */
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            margin: 20px auto 30px auto; /* Added for spacing and centering */
        }

        .carousel-track {
            display: flex;
            transition: transform 0.5s ease-in-out;
        }

        .carousel-banner {
            min-width: 100%;
            flex-shrink: 0;
            height: 180px; /* Adjusted height - Reduced from 220px */
            display: flex;
            color: white;
            box-sizing: border-box;
            align-items: stretch; /* Make inner content stretch */
            justify-content: stretch;
        }

        /* Common structure for all banners now: .app-banner-content */
        .app-banner-content {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            text-align: left;
            padding: 20px;
            box-sizing: border-box;
            position: relative; /* For potential absolute positioned elements like badges */
        }

        .app-banner-text-area {
            flex-basis: 58%; /* Adjust as needed */
            padding-right: 15px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .app-banner-image-area {
            flex-basis: 38%; /* Adjust as needed */
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            overflow: hidden; /* To contain image nicely */
            border-radius: 8px; /* Rounded corners for image area */
        }

        .app-banner-image-area img {
            max-width: 100%;
            max-height: 100px; /* Control image height - Reduced from 130px */
            height: auto;
            border-radius: 6px;
            display: block;
            object-fit: contain; /* Or \'\'\'cover\'\'\' depending on image aspect ratio */
        }

        .app-banner-text-area h2 {
            font-size: 1.4em; /* Adjusted for new banner height */
            font-weight: 700;
            margin-top: 0;
            margin-bottom: 8px;
            line-height: 1.2;
            font-family: 'Poppins', sans-serif; /* Added Poppins */
        }

        .app-banner-text-area p.tagline, .app-banner-text-area ul {
            font-size: 0.75em; /* Smaller for subtext/list */
            margin-bottom: 12px;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.9);
            font-family: 'Poppins', sans-serif; /* Added Poppins */
        }
         .app-banner-text-area ul {
            list-style: none; /* Remove default bullets */
            padding-left: 0;
        }
        .app-banner-text-area ul li {
            margin-bottom: 4px;
            display: flex;
            align-items: flex-start;
        }
        .app-banner-text-area ul li::before { /* Custom bullet using Unicode */
            content: \'✓\'; /* Checkmark */
            margin-right: 8px;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.9);
        }


        .app-banner-cta-button {
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.75em; /* Smaller button text */
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            margin-top: auto; /* Pushes button to bottom if text area is flex column */
            background-color: #ffffff; /* Default white button */
            color: #333333; /* Default dark text for white button */
            font-family: 'Poppins', sans-serif; /* Added Poppins */
        }
        .app-banner-cta-button:hover { opacity: 0.85; transform: translateY(-1px); }

        /* Offer Highlight Badge */
        .offer-badge {
            background-color: #FFD700; /* Gold color for offer */
            color: #333;
            font-size: 0.7em;
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: 700;
            display: inline-block;
            margin-top: 5px;
            font-family: 'Poppins', sans-serif; /* Added Poppins */
        }


        /* Specific Banner Backgrounds (Gradients) & Button Text Colors */
        #banner1 { background: linear-gradient(135deg, #005f73 0%, #007991 100%); }
        #banner1 .app-banner-cta-button { color: #005f73; }

        #banner2 { background: linear-gradient(135deg, #ff7f50 0%, #ffa07a 100%); }
        #banner2 .app-banner-cta-button { color: #ff7f50; }

        #banner3 { background: linear-gradient(135deg, #28a745 0%, #34cc54 100%); }
        #banner3 .app-banner-cta-button { color: #28a745; }

        #banner4 { background: linear-gradient(135deg, #6f42c1 0%, #8d5fd3 100%); }
        #banner4 .app-banner-cta-button { color: #6f42c1; }

        #banner1-clone { background: linear-gradient(135deg, #005f73 0%, #007991 100%); }


        .carousel-button { /* Prev/Next Buttons */
            position: absolute; top: 50%; transform: translateY(-50%);
            background-color: rgba(0, 0, 0, 0.3); color: white; border: none;
            padding: 8px; font-size: 18px; cursor: pointer; z-index: 10;
            border-radius: 50%; width: 30px; height: 30px; display: flex;
            justify-content: center; align-items: center;
        }
        .carousel-button.prev { left: 8px; }
        .carousel-button.next { right: 8px; }


        /* Responsive adjustments */
        @media (max-width: 480px) { /* Targeting smaller mobile screens more specifically */
            .carousel-banner { height: 160px; } /* Slightly smaller height - Reduced from 200px */
            .app-banner-content { padding: 15px; flex-direction: column; text-align:center; }
            .app-banner-image-area {
                flex-basis: auto; width: 50%; max-height: 60px; margin: 0 auto 10px auto; order: -1; /* Image on top - Reduced from 70px */
            }
            .app-banner-text-area {
                flex-basis: auto; width: 100%; padding-right: 0; text-align: center;
            }
            .app-banner-text-area h2 { font-size: 1.2em; margin-bottom: 5px; }
            .app-banner-text-area p.tagline, .app-banner-text-area ul { font-size: 0.7em; margin-bottom: 8px; }
            .app-banner-text-area ul li::before { margin-right: 5px; }
            .app-banner-cta-button { font-size: 0.7em; padding: 6px 12px; width: 80%; margin: 5px auto 0 auto; }
            .offer-badge { font-size: 0.65em; }
        }
    /* End of CSS for New Carousel Banners */

    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Leaflet CSS REMOVED -->
    <!-- Google Maps API Script is included further down -->
</head>
<body>
    <div class="profile-sidebar" id="profileSidebar">
        <div class="profile-sidebar-header">
            <h3>Profile</h3>
            <span class="close-sidebar-btn" id="closeSidebarBtn">&times;</span>
        </div>
        <div class="profile-sidebar-user">
            <i class="fas fa-user-circle"></i>
            <p id="sidebarUserName">Aman CEO</p>
        </div>
        <nav>
            <ul>
                <li><a href="#" id="myAccountLink">My Account</a></li>
                <li><a href="#">My Bookings</a></li>
                <li><a href="#">Terms & Conditions</a></li>
                <li><a href="#">Contact Us</a></li>
                <li><a href="#" id="logoutLink">Logout</a></li>
            </ul>
        </nav>
    </div>

    <header>
        <nav>
            <div class="header-top-row"> <!-- Wrapper for User Info and Delivery Time -->
                <div class="user-info" id="profileIconContainer">
                    <i class="fas fa-user-circle"></i>
                    <span>Aman CEO</span>
                </div>
                <div class="delivery-time-indicator">
                    <div class="delivery-time-value">30</div>
                    <div class="delivery-time-unit">min</div>
                </div>
            </div>
            <div class="location">
                <i class="fas fa-map-marker-alt"></i>
                <span id="headerLocationText">Select Location</span>
                <i class="fas fa-chevron-down"></i>
            </div>
        </nav>
        <!-- Search bar below was removed by user request to revert -->
    </header>

    <main>
        <div id="homePageDefaultContent">
            <div class="items-container">
              <div class="consultation-item item1">
                <div class="item-image-box">
                  <img src="https://via.placeholder.com/140x90/F0F3F9/808080?text=Book+In-Clinic" alt="Book In-Clinic" class="item-image">

                </div>
                <div class="item-caption">
                  <p class="item-title">Book Smart Doctor Visit</p>
                  <div class="item-arrow"></div>
                </div>
              </div>

              <div class="consultation-item item2">
                <div class="item-image-box">
                  <img src="https://via.placeholder.com/140x90/E3F2FD/808080?text=Video+Consult" alt="Video Consultation" class="item-image">
                </div>
                <div class="item-caption">
                  <p class="item-title"> Book Nurse Visit</p>
                  <div class="item-arrow"></div>
                </div>
              </div>

              <div class="consultation-item item3">
                <div class="item-image-box">
                  <img src="https://via.placeholder.com/140x90/E0F7FA/808080?text=Home+Care" alt="Home Care Service" class="item-image">
                </div>
                <div class="item-caption">
                  <p class="item-title">Home Care Service</p>
                  <div class="item-arrow"></div>
                </div>
              </div>

              <div class="consultation-item item4">
                <div class="item-image-box">
                  <img src="https://via.placeholder.com/140x90/FFF3E0/808080?text=Emergency" alt="Emergency smart doctor Visit" class="item-image">
                </div>
                <div class="item-caption">
                  <p class="item-title">Emergency Smart Doctor Visit</p>
                  <div class="item-arrow"></div>
                </div>
              </div>
            </div>

            <div class="carousel-container">
                <div class="carousel-track">
                    <div class="carousel-banner" id="banner1">
                        <div class="app-banner-content">
                            <div class="app-banner-text-area">
                                <h2>Home Care Service</h2>
                                <p class="tagline">Compassionate support at home.</p>
                                <ul>
                                    <li>Daily living assistance</li>
                                    <li>Medication management</li>
                                    <li>Mobility support</li>
                                </ul>
                                <button class="app-banner-cta-button">Explore Services</button>
                            </div>
                            <div class="app-banner-image-area">
                                 <img src="https://via.placeholder.com/150x100/FFFFFF/005f73?text=Caregiver" alt="Home Care">
                            </div>
                        </div>
                    </div>

                    <div class="carousel-banner" id="banner2">
                        <div class="app-banner-content">
                            <div class="app-banner-text-area">
                                <h2>Smart Doctor Visit</h2>
                                <ul>
                                    <li>Instant medicine at home</li>
                                    <li>7 days follow-up</li>
                                    <li>Very low cost</li>
                                </ul>
                                <div class="offer-badge">₹50 OFF First Visit!</div>
                                <button class="app-banner-cta-button">Book Visit</button>
                            </div>
                            <div class="app-banner-image-area">
                                <img src="https://via.placeholder.com/150x100/FFFFFF/ff7f50?text=Doctor+Online" alt="Doctor Visit">
                            </div>
                        </div>
                    </div>

                    <div class="carousel-banner" id="banner3">
                         <div class="app-banner-content">
                            <div class="app-banner-text-area">
                                <h2>Emergency Service</h2>
                                <p class="tagline">24x7 Quick Response</p>
                                <ul>
                                    <li>Bike ambulance team</li>
                                    <li>Trained professionals</li>
                                    <li>Minor emergencies covered</li>
                                </ul>
                                <button class="app-banner-cta-button">Get Help Now</button>
                            </div>
                            <div class="app-banner-image-area">
                                <img src="https://via.placeholder.com/150x100/FFFFFF/28a745?text=Emergency" alt="Emergency Service">
                            </div>
                        </div>
                    </div>

                    <div class="carousel-banner" id="banner4">
                        <div class="app-banner-content">
                            <div class="app-banner-text-area">
                                <h2>Online Pharmacy</h2>
                                <p class="tagline">Medicines at your doorstep.</p>
                                <ul>
                                    <li>Wide range available</li>
                                    <li>Easy prescription upload</li>
                                    <li>Fast & reliable delivery</li>
                                </ul>
                                <button class="app-banner-cta-button">Shop Medicines</button>
                            </div>
                            <div class="app-banner-image-area">
                                <img src="https://via.placeholder.com/150x100/FFFFFF/6f42c1?text=Pharmacy" alt="Online Pharmacy">
                            </div>
                        </div>
                    </div>

                    <div class="carousel-banner" id="banner1-clone">
                         <div class="app-banner-content">
                             <div class="app-banner-text-area">
                                <h2>Home Care Service</h2>
                                <p class="tagline">Compassionate support at home.</p>
                                <ul>
                                    <li>Daily living assistance</li>
                                    <li>Medication management</li>
                                    <li>Mobility support</li>
                                </ul>
                                <button class="app-banner-cta-button">Explore Services</button>
                            </div>
                            <div class="app-banner-image-area">
                                 <img src="https://via.placeholder.com/150x100/FFFFFF/005f73?text=Caregiver" alt="Home Care">
                            </div>
                        </div>
                    </div>
                </div>
                <button class="carousel-button prev" onclick="moveBannerManually(-1)">&#10094;</button>
                <button class="carousel-button next" onclick="moveBannerManually(1)">&#10095;</button>
            </div>

            <section class="services">
                <h2>Services</h2>
                <div class="service-item">
                    <img src="assets/img/homescreenicon.png" alt="Doctor Appointment">
                    <div class="service-details">
                        <h3>Doctor appointment</h3>
                        <ul>
                            <li>Certified doctors</li>
                            <li>Home visit available</li>
                            <li>Easy online booking</li>
                        </ul>
                    </div>
                </div>
                <div class="service-item">
                    <img src="assets/img/homescreenicon.png" alt="Nurse Visit">
                    <div class="service-details">
                        <h3>Nurse visit</h3>
                        <ul>
                            <li>Professional nurses</li>
                            <li>24/7 support</li>
                            <li>Personalized care</li>
                        </ul>
                    </div>
                </div>
                <div class="service-item">
                    <img src="assets/img/homescreenicon.png" alt="Home Service Detailed">
                    <div class="service-details">
                        <h3>Home Service</h3>
                        <ul>
                            <li>Convenient at-home care</li>
                            <li>Flexible scheduling</li>
                            <li>Trusted by families</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section class="faqs">
                <h2>FAQs</h2>
                <div class="faq-item">
                    <p>What is DhipyCare?</p>
                    <i class="fas fa-plus"></i>
                </div>
                <div class="faq-answer">
                    <p>DhipyCare is a comprehensive healthcare service provider offering doctor appointments, nursing services, and home care solutions. We aim to make healthcare accessible and convenient.</p>
                </div>

                <div class="faq-item">
                    <p>How do I book a doctor?</p>
                    <i class="fas fa-plus"></i>
                </div>
                <div class="faq-answer">
                    <p>You can easily book a doctor through our "Book a Doctor" section on the homepage. Select your preferred doctor, choose a time slot, and confirm your appointment. It's that simple!</p>
                </div>

                <div class="faq-item">
                    <p>Is my data secure?</p>
                    <i class="fas fa-plus"></i>
                </div>
                <div class="faq-answer">
                    <p>Yes, we take data security very seriously. All your personal and medical information is encrypted and stored securely in compliance with industry best practices and privacy regulations.</p>
                </div>
            </section>
        </div> <!-- End of homePageDefaultContent -->

        <div id="doctorBrowseView" style="display: none;">
            <div class="account-page-header"> <!-- Reusing this class for alignment and style -->
                <button class="back-btn" id="backToHomeDefaultBtn"><i class="fas fa-arrow-left"></i><span class="back-btn-text"> Back</span></button>
                <h2 class="doctor-browse-view-title">Meet Our Doctors</h2>
            </div>
            <section class="doctor-list-section">
                <div class="doctor-scroll-container" id="doctorScrollContainer">
                    <!-- Placeholder Doctor Cards -->
                    <div class="doctor-card">
                        <img src="https://via.placeholder.com/100/3A5A9F/FFFFFF?Text=Doc" alt="Dr. Vikram Patel">
                        <h3>Dr. Vikram Patel</h3>
                        <p>BDS, Dental Care</p>
                        <p>10 yrs exp.</p>
                    </div>
                    <div class="doctor-card">
                        <img src="https://via.placeholder.com/100/2ECC71/FFFFFF?Text=Doc" alt="Dr. Sunita Rao">
                        <h3>Dr. Sunita Rao</h3>
                        <p>MD, Gynecology</p>
                        <p>7 yrs exp.</p>
                    </div>
                    <div class="doctor-card">
                        <img src="https://via.placeholder.com/100/E74C3C/FFFFFF?Text=Doc" alt="Dr. Priya Sharma">
                        <h3>Dr. Priya Sharma</h3>
                        <p>MD, General Physician</p>
                        <p>5 yrs exp.</p>
                    </div>
                    <div class="doctor-card">
                        <img src="https://via.placeholder.com/100/F39C12/FFFFFF?Text=Doc" alt="Dr. Rohan Mehra">
                        <h3>Dr. Rohan Mehra</h3>
                        <p>MBBS, Pediatrics</p>
                        <p>8 yrs exp.</p>
                    </div>
                    <div class="doctor-card">
                        <img src="https://via.placeholder.com/100/9B59B6/FFFFFF?Text=Doc" alt="Dr. Anjali Singh">
                        <h3>Dr. Anjali Singh</h3>
                        <p>MD, Dermatology</p>
                        <p>6 yrs exp.</p>
                    </div>
                     <div class="doctor-card">
                        <img src="https://via.placeholder.com/100/3498DB/FFFFFF?Text=Doc" alt="Dr. Vikram Patel">
                        <h3>Dr. Vikram Patel</h3>
                        <p>BDS, Dental Care</p>
                        <p>10 yrs exp.</p>
                    </div>
                    <div class="doctor-card">
                        <img src="https://via.placeholder.com/100/1ABC9C/FFFFFF?Text=Doc" alt="Dr. Sunita Rao">
                        <h3>Dr. Sunita Rao</h3>
                        <p>MD, Gynecology</p>
                        <p>7 yrs exp.</p>
                    </div>
                    <!-- Add more cards as needed -->
                </div>
            </section>

            <section class="concerns-section">
                <h2>What are you concerned about?</h2>
                <div class="concerns-grid">
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Stomach Pain</p>
                    </div>
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Vertigo</p>
                    </div>
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Acne</p>
                    </div>
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>PCOS</p>
                    </div>
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Thyroid</p>
                    </div>
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Headaches</p>
                    </div>
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Fungal Infection</p>
                    </div>
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Back Pain</p>
                    </div>
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Cold & Flu</p>
                    </div>
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Fever</p>
                    </div>
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Diabetes Care</p>
                    </div>
                    <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Hypertension</p>
                    </div>
                     <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Allergies</p>
                    </div>
                     <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Hair Fall</p>
                    </div>
                     <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Anxiety</p>
                    </div>
                     <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Depression</p>
                    </div>
                     <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Joint Pain</p>
                    </div>
                     <div class="concern-item">
                        <div class="concern-icon">Icon</div>
                        <p>Digestive Issues</p>
                    </div>
                    <!-- Add more concerns as needed -->
                </div>
            </section>
        </div> <!-- End of doctorBrowseView -->

        <div id="nurseBrowseView" style="display: none;">
            <div class="account-page-header">
                <button class="back-btn" id="backToHomeFromNursesBtn"><i class="fas fa-arrow-left"></i><span class="back-btn-text"> Back</span></button>
                <h2 class="doctor-browse-view-title">Meet Our Nurses</h2> <!-- Reusing title style -->
            </div>
            <section class="doctor-list-section"> <!-- Reusing section style -->
                <div class="doctor-scroll-container" id="nurseScrollContainer">
                    <!-- Placeholder Nurse Cards -->
                    <div class="doctor-card"> <!-- Reusing card style -->
                        <img src="https://via.placeholder.com/100/FF69B4/FFFFFF?Text=Nurse" alt="Nurse Anika">
                        <h3>Nurse Anika</h3>
                        <p>RN, BSN</p>
                        <p>5 yrs exp.</p>
                    </div>
                    <div class="doctor-card">
                        <img src="https://via.placeholder.com/100/FFC0CB/000000?Text=Nurse" alt="Nurse Ben">
                        <h3>Nurse Ben</h3>
                        <p>LPN</p>
                        <p>8 yrs exp.</p>
                    </div>
                    <div class="doctor-card">
                        <img src="https://via.placeholder.com/100/FF69B4/FFFFFF?Text=Nurse" alt="Nurse Chloe">
                        <h3>Nurse Chloe</h3>
                        <p>RN, Geriatrics</p>
                        <p>12 yrs exp.</p>
                    </div>
                    <!-- Add more nurse cards as needed -->
                </div>
            </section>
            <section class="concerns-section"> <!-- Reusing concerns section -->
                <h2>What are you concerned about?</h2>
                <div class="concerns-grid">
                    <div class="concern-item"><div class="concern-icon">Icon</div><p>Post-Operative Care</p></div>
                    <div class="concern-item"><div class="concern-icon">Icon</div><p>Elderly Care</p></div>
                    <div class="concern-item"><div class="concern-icon">Icon</div><p>Wound Dressing</p></div>
                    <div class="concern-item"><div class="concern-icon">Icon</div><p>Injections</p></div>
                    <!-- Add more relevant concerns or keep generic -->
                </div>
            </section>
        </div> <!-- End of nurseBrowseView -->

        <div id="homeServiceBrowseView" style="display: none;">
            <div class="account-page-header">
                <button class="back-btn" id="backToHomeFromHomeServicesBtn"><i class="fas fa-arrow-left"></i><span class="back-btn-text"> Back</span></button>
                <h2 class="doctor-browse-view-title">Explore Home Services</h2> <!-- Reusing title style -->
            </div>
            <section class="doctor-list-section"> <!-- Reusing section style -->
                <div class="doctor-scroll-container" id="homeServiceScrollContainer">
                    <!-- Placeholder Home Service Cards -->
                    <div class="doctor-card"> <!-- Reusing card style -->
                        <img src="https://via.placeholder.com/100/708090/FFFFFF?Text=Service" alt="Physiotherapy">
                        <h3>Physiotherapy</h3>
                        <p>At-home sessions</p>
                        <p>Certified therapists</p>
                    </div>
                    <div class="doctor-card">
                        <img src="https://via.placeholder.com/100/A9A9A9/FFFFFF?Text=Service" alt="Lab Tests">
                        <h3>Lab Tests at Home</h3>
                        <p>Sample collection</p>
                        <p>Quick reports</p>
                    </div>
                    <div class="doctor-card">
                        <img src="https://via.placeholder.com/100/708090/FFFFFF?Text=Service" alt="Medical Equipment">
                        <h3>Medical Equipment</h3>
                        <p>Rental & Purchase</p>
                        <p>Delivery & Setup</p>
                    </div>
                    <!-- Add more home service cards as needed -->
                </div>
            </section>
            <section class="concerns-section"> <!-- Reusing concerns section -->
                <h2>How can we assist you?</h2> <!-- Slightly different title for this context -->
                <div class="concerns-grid">
                    <div class="concern-item"><div class="concern-icon">Icon</div><p>Rehabilitation</p></div>
                    <div class="concern-item"><div class="concern-icon">Icon</div><p>Diagnostics</p></div>
                    <div class="concern-item"><div class="concern-icon">Icon</div><p>Daily Assistance</p></div>
                    <div class="concern-item"><div class="concern-icon">Icon</div><p>Wellness</p></div>
                    <!-- Add more relevant concerns or keep generic -->
                </div>
            </section>
        </div> <!-- End of homeServiceBrowseView -->

        <!-- Health Issue Form View -->
        <div id="healthIssueFormView" style="display: none;">
            <div class="account-page-header">
                <button class="back-btn" id="backToBrowseViewBtn"><i class="fas fa-arrow-left"></i><span class="back-btn-text"> Back</span></button>
                <h2 class="doctor-browse-view-title" id="healthIssueFormTitle">Book a Consultation</h2>
            </div>
            <form id="healthIssueForm" class="health-issue-form" autocomplete="off">
                <div class="form-group">
                    <label for="patientName">Patient Name</label>
                    <input type="text" id="patientName" name="patientName" required>
                </div>
                <div class="form-group">
                    <label for="patientAge">Age</label>
                    <input type="number" id="patientAge" name="patientAge" min="0" max="120" required>
                </div>
                <div class="form-group">
                    <label for="patientGender">Gender</label>
                    <select id="patientGender" name="patientGender" required>
                        <option value="">Select</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="patientPhone">Phone Number</label>
                    <input type="tel" id="patientPhone" name="patientPhone" pattern="[0-9]{10}" required>
                </div>
                <div class="form-group">
                    <label for="patientAddress">Address</label>
                    <textarea id="patientAddress" name="patientAddress" rows="2" required></textarea>
                </div>
                <div class="form-group">
                    <label for="paymentMethod">Payment Method</label>
                    <select id="paymentMethod" name="paymentMethod" required>
                        <option value="">Select</option>
                        <option value="cash">Cash</option>
                        <option value="upi">UPI</option>
                        <option value="card">Card</option>
                    </select>
                </div>
                <button type="submit" class="confirm-location-btn" style="margin-top:20px;">Submit</button>
            </form>
        </div>
    </main>

    <div class="account-page" id="accountPage">
        <div class="account-page-header">
            <button class="back-btn" id="backToMainBtn"><i class="fas fa-arrow-left"></i> Back</button>
            <!-- Title can be dynamic if needed -->
        </div>
        <div class="account-profile-summary">
            <i class="fas fa-user-circle"></i>
            <h3 id="accountPageUserName">N/A</h3>
            <input type="text" id="accountPageUserNameInput" class="edit-input" style="display: none;">
        </div>
        <div class="account-details-grid">
            <div class="detail-item">
                <label>Email:</label>
                <p id="accountEmail">N/A</p>
                <input type="email" id="accountEmailInput" class="edit-input" style="display: none;" readonly> <!-- Email usually not editable directly by user -->
            </div>
            <div class="detail-item">
                <label>Phone:</label>
                <p id="accountPhone">N/A</p>
                <input type="tel" id="accountPhoneInput" class="edit-input" style="display: none;">
            </div>
            <div class="detail-item">
                <label>Gender:</label>
                <p id="accountGender">N/A</p>
                <select id="accountGenderInput" class="edit-input" style="display: none;">
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                    <option value="prefer_not_to_say">Prefer not to say</option>
                </select>
            </div>
            <div class="detail-item">
                <label>Date of Birth:</label>
                <p id="accountDob">N/A</p>
                <input type="date" id="accountDobInput" class="edit-input" style="display: none;">
            </div>
        </div>
        <div class="profile-edit-actions">
            <button class="edit-profile-btn" id="editProfileBtn">Edit Profile</button>
            <button class="edit-profile-btn" id="saveProfileBtn" style="display: none;">Save Changes</button>
            <button class="edit-profile-btn" id="cancelEditBtn" style="background-color: #7f8c8d; display: none;">Cancel</button>
        </div>
    </div>

    <!-- Custom Logout Confirmation Modal -->
    <div id="logoutConfirmationModal" class="logout-confirm-modal">
        <div class="logout-confirm-modal-content">
            <h3>Are you sure you want to logout?</h3>
            <div class="logout-confirm-buttons">
                <button id="confirmLogoutBtn">Yes, Logout</button>
                <button id="cancelLogoutBtn">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Location Selection Modal -->
    <div id="locationSelectionModal" class="location-modal">
        <div class="location-modal-content">
            <span class="close-modal-btn" id="closeLocationModalBtn">&times;</span>
            <h2>Select Your Location</h2>

            <input type="text" id="locationSearchInput" class="location-search-input" placeholder="Type your location...">
            <button id="useCurrentLocationBtn" class="current-location-btn"><i class="fas fa-map-marker-alt"></i> Use My Current Location</button>
            <div id="currentLocationStatusMsg" style="font-size: 0.8em; color: #555; margin-top: 5px; display: none;"></div>

            <div id="map" class="map-placeholder">
                <!-- Google Map will be initialized here -->
            </div>
            <div id="selectedAddress"></div>
            <button id="confirmLocationBtn" class="confirm-location-btn">Continue</button>
        </div>
    </div>

    <footer>
        <!-- Optional footer content -->
    </footer>

    <script>
document.addEventListener('DOMContentLoaded', () => {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        item.addEventListener('click', () => {
            const icon = item.querySelector('i');
            // The answer div is expected to be the next sibling element
            const answer = item.nextElementSibling;

            // Toggle active class on the clicked item
            item.classList.toggle('active');

            if (item.classList.contains('active')) {
                icon.classList.remove('fa-plus');
                icon.classList.add('fa-minus');
                if (answer && answer.classList.contains('faq-answer')) {
                    answer.style.display = 'block';
                }
            } else {
                icon.classList.remove('fa-minus');
                icon.classList.add('fa-plus');
                if (answer && answer.classList.contains('faq-answer')) {
                    answer.style.display = 'none';
                }
            }

            // Optional: Close other FAQ items when one is opened
            faqItems.forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('active')) {
                    otherItem.classList.remove('active');
                    otherItem.querySelector('i').classList.remove('fa-minus');
                    otherItem.querySelector('i').classList.add('fa-plus');
                    const otherAnswer = otherItem.nextElementSibling;
                    if (otherAnswer && otherAnswer.classList.contains('faq-answer')) {
                        otherAnswer.style.display = 'none';
                    }
                }
            });
        });
    });

    // Profile Sidebar & Account Page Logic
    const profileIconContainer = document.getElementById('profileIconContainer');
    const profileSidebar = document.getElementById('profileSidebar');
    const closeSidebarBtn = document.getElementById('closeSidebarBtn');
    const myAccountLink = document.getElementById('myAccountLink');
    const accountPage = document.getElementById('accountPage');
    const mainContent = document.querySelector('main'); // Assuming your main content is in <main>
    const backToMainBtn = document.getElementById('backToMainBtn');

    // Page view elements
    const homePageDefaultContent = document.getElementById('homePageDefaultContent');
    const doctorBrowseView = document.getElementById('doctorBrowseView');
    const nurseBrowseView = document.getElementById('nurseBrowseView'); // New
    const homeServiceBrowseView = document.getElementById('homeServiceBrowseView'); // New

    // Action Cards
    const bookDoctorCard = document.getElementById('bookDoctorCard');
    const bookNurseCard = document.getElementById('bookNurseCard'); // New reference
    const homeServiceCard = document.getElementById('homeServiceCard'); // New reference

    // Back Buttons for Browse Views
    const backToHomeDefaultBtn = document.getElementById('backToHomeDefaultBtn');
    const backToHomeFromNursesBtn = document.getElementById('backToHomeFromNursesBtn'); // New
    const backToHomeFromHomeServicesBtn = document.getElementById('backToHomeFromHomeServicesBtn'); // New

    // Account page elements for editing
    const editProfileBtn = document.getElementById('editProfileBtn');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const cancelEditBtn = document.getElementById('cancelEditBtn');

    const accountPageUserName = document.getElementById('accountPageUserName');
    const accountPageUserNameInput = document.getElementById('accountPageUserNameInput');
    const accountEmail = document.getElementById('accountEmail');
    const accountEmailInput = document.getElementById('accountEmailInput'); // Email input might be readonly
    const accountPhone = document.getElementById('accountPhone');
    const accountPhoneInput = document.getElementById('accountPhoneInput');
    const accountGender = document.getElementById('accountGender');
    const accountGenderInput = document.getElementById('accountGenderInput');
    const accountDob = document.getElementById('accountDob');
    const accountDobInput = document.getElementById('accountDobInput');

    let isEditMode = false;
    let originalUserData = {}; // To store data for cancel

    // Function to clear all account page fields and reset originalUserData
    function clearAccountPageFields() {
        if (accountPageUserName) accountPageUserName.textContent = 'N/A';
        if (accountEmail) accountEmail.textContent = 'N/A';
        if (accountPhone) accountPhone.textContent = 'N/A';
        if (accountGender) accountGender.textContent = 'N/A';
        if (accountDob) accountDob.textContent = 'N/A';

        if (accountPageUserNameInput) accountPageUserNameInput.value = '';
        if (accountEmailInput) accountEmailInput.value = '';
        if (accountPhoneInput) accountPhoneInput.value = '';
        if (accountGenderInput) accountGenderInput.value = 'male'; // Or your preferred default
        if (accountDobInput) accountDobInput.value = '';

        originalUserData = {}; // Reset cached user data
    }

    // Function to show sidebar
    function openSidebar() {
        if (profileSidebar) profileSidebar.classList.add('active');
    }

    // Function to close sidebar
    function closeSidebar() {
        if (profileSidebar) profileSidebar.classList.remove('active');
    }

    // Function to show Account Page and hide main content
    function showAccountPage() {
        if (mainContent) mainContent.classList.add('hidden');
        if (accountPage) accountPage.style.display = 'block';
        closeSidebar(); // Close sidebar when navigating to account page
        if (auth && auth.currentUser) {
            fetchAndDisplayUserData(auth.currentUser.uid);
        } else {
            console.log("No user logged in. Cannot fetch data for Account Page.");
            // Display default/placeholder data or prompt for login
            accountPageUserName.textContent = 'N/A';
            accountEmail.textContent = 'N/A';
            accountPhone.textContent = 'N/A';
            accountGender.textContent = 'N/A';
            accountDob.textContent = 'N/A';
        }
    }

    // Function to show main content and hide Account Page
    function showMainContent() {
        const homePageDefaultContent = document.getElementById('homePageDefaultContent');
        const doctorBrowseView = document.getElementById('doctorBrowseView');

        if (mainContent) mainContent.classList.remove('hidden');
        if (accountPage) accountPage.style.display = 'none';

        // When showing main content, ensure we show the *default* home view, not doctor browse view
        if (homePageDefaultContent) homePageDefaultContent.style.display = 'block';
        if (doctorBrowseView) doctorBrowseView.style.display = 'none';
        if (nurseBrowseView) nurseBrowseView.style.display = 'none'; // Hide nurse view
        if (homeServiceBrowseView) homeServiceBrowseView.style.display = 'none'; // Hide home service view
    }

    // Event Listeners
    if (profileIconContainer) {
        profileIconContainer.addEventListener('click', openSidebar);
    }

    if (closeSidebarBtn) {
        closeSidebarBtn.addEventListener('click', closeSidebar);
    }

    if (myAccountLink) {
        myAccountLink.addEventListener('click', (e) => {
            e.preventDefault(); // Prevent default anchor behavior
            showAccountPage();
        });
    }

    if (backToMainBtn) {
        backToMainBtn.addEventListener('click', () => {
            showMainContent();
        });
    }

    // Clicking outside the sidebar to close it (optional)
    document.addEventListener('click', (event) => {
        if (profileSidebar && profileSidebar.classList.contains('active')) {
            if (!profileSidebar.contains(event.target) && !profileIconContainer.contains(event.target)) {
                closeSidebar();
            }
        }
    });

    // Logout functionality (placeholder)
    const logoutLink = document.getElementById('logoutLink');
    const logoutConfirmationModal = document.getElementById('logoutConfirmationModal');
    const confirmLogoutBtn = document.getElementById('confirmLogoutBtn');
    const cancelLogoutBtn = document.getElementById('cancelLogoutBtn');

    if (logoutLink) {
        logoutLink.addEventListener('click', (e) => {
            e.preventDefault();
            if (logoutConfirmationModal) logoutConfirmationModal.style.display = 'flex'; // Show custom modal
        });
    }

    if (cancelLogoutBtn) {
        cancelLogoutBtn.addEventListener('click', () => {
            if (logoutConfirmationModal) logoutConfirmationModal.style.display = 'none'; // Hide custom modal
        });
    }

    if (confirmLogoutBtn) {
        confirmLogoutBtn.addEventListener('click', () => {
            if (logoutConfirmationModal) logoutConfirmationModal.style.display = 'none'; // Hide custom modal
            // Proceed with Firebase logout
            if (firebase && firebase.auth) {
                firebase.auth().signOut().then(() => {
                    // alert('Successfully logged out.'); // Removed alert
                    document.getElementById('sidebarUserName').textContent = 'Guest';
                    const headerUserNameElement = profileIconContainer.querySelector('span');
                    if(headerUserNameElement) headerUserNameElement.textContent = 'Guest';
                    showMainContent();
                    closeSidebar();
                    if (isEditMode) {
                       toggleEditMode(false); // This internally calls fetchAndDisplayUserData(null)
                    }
                    clearAccountPageFields(); // Explicitly clear fields before redirect
                    // Redirect to login page after logout
                    window.location.href = 'index_login.html';
                }).catch((error) => {
                    console.error("Logout error: ", error);
                    alert('Error logging out: ' + error.message);
                });
            } else {
                alert('Firebase not initialized. Cannot logout.');
            }
        });
    }

    // Function to show a specific browse view and hide others
    function showBrowseView(viewToShow) {
        const views = [homePageDefaultContent, doctorBrowseView, nurseBrowseView, homeServiceBrowseView, accountPage];
        views.forEach(view => {
            if (view) {
                view.style.display = (view === viewToShow) ? 'block' : 'none';
            }
        });
        if (mainContent && mainContent.classList.contains('hidden') && viewToShow !== accountPage) {
             mainContent.classList.remove('hidden');
        }
        window.scrollTo(0, 0);
    }

    // Redirect for Book a Doctor card
    if (bookDoctorCard) {
        bookDoctorCard.addEventListener('click', () => {
            showBrowseView(doctorBrowseView);
        });
    }

    // Event listener for Book a Nurse card
    if (bookNurseCard) {
        bookNurseCard.addEventListener('click', () => {
            showBrowseView(nurseBrowseView);
        });
    }

    // Event listener for Home Service card
    if (homeServiceCard) {
        homeServiceCard.addEventListener('click', () => {
            showBrowseView(homeServiceBrowseView);
        });
    }

    // JS for the new Back button in doctorBrowseView
    if (backToHomeDefaultBtn) {
        backToHomeDefaultBtn.addEventListener('click', () => {
            showMainContent();
        });
    }

    // JS for the Back button in nurseBrowseView
    if (backToHomeFromNursesBtn) {
        backToHomeFromNursesBtn.addEventListener('click', () => {
            showMainContent();
        });
    }

    // JS for the Back button in homeServiceBrowseView
    if (backToHomeFromHomeServicesBtn) {
        backToHomeFromHomeServicesBtn.addEventListener('click', () => {
            showMainContent();
        });
    }

    // --- Event Listeners for the new consultation items ---
    const smartDoctorVisitCard = document.querySelector('.items-container .item1');
    const nurseVisitCard = document.querySelector('.items-container .item2');
    const homeCareServiceCard = document.querySelector('.items-container .item3');
    const emergencyDoctorVisitCard = document.querySelector('.items-container .item4');

    if (smartDoctorVisitCard && doctorBrowseView) {
        smartDoctorVisitCard.addEventListener('click', () => {
            showBrowseView(doctorBrowseView);
        });
    }

    if (nurseVisitCard && nurseBrowseView) {
        nurseVisitCard.addEventListener('click', () => {
            showBrowseView(nurseBrowseView);
        });
    }

    if (homeCareServiceCard && homeServiceBrowseView) {
        homeCareServiceCard.addEventListener('click', () => {
            showBrowseView(homeServiceBrowseView);
        });
    }

    // --- Generalized Auto-sliding Logic for Multiple Carousels ---
    function initializeCarousel(viewElementId, scrollContainerId, cardsSelector) {
        const viewElement = document.getElementById(viewElementId);
        const scrollContainer = document.getElementById(scrollContainerId);
        if (!viewElement || !scrollContainer) {
            console.warn(`Carousel elements not found for ${viewElementId}`);
            return null;
        }

        let animationFrameId_carousel;
        let isViewVisible_carousel = false;
        let isMouseHovering_carousel = false;
        const scrollSpeed_carousel = 0.5;
        let hasCloned_carousel = false; // Prevent re-cloning if view is rapidly toggled

        function continuousScrollLoop_carousel() {
            if (!isViewVisible_carousel || isMouseHovering_carousel) {
                if (animationFrameId_carousel) cancelAnimationFrame(animationFrameId_carousel);
                return;
            }
            scrollContainer.scrollLeft += scrollSpeed_carousel;
            if (scrollContainer.scrollLeft >= scrollContainer.scrollWidth / 2) {
                scrollContainer.scrollLeft = 0;
            }
            animationFrameId_carousel = requestAnimationFrame(continuousScrollLoop_carousel);
        }

        function prepareInfiniteScroll_carousel() {
            if (hasCloned_carousel || !scrollContainer.children.length) return;
            const originalCards = Array.from(scrollContainer.children);
            originalCards.forEach(card => card.classList.remove('clone')); // Clean up any stray clones from bad state

            const cardsToClone = Array.from(scrollContainer.querySelectorAll(cardsSelector + ':not(.clone)'));
             if (cardsToClone.length === 0 && originalCards.length > 0) { // If all children are already cards, use them
                //This case might happen if cardsSelector is just .doctor-card and all children are already that.
            } else if (cardsToClone.length === 0) {
                return; // No cards to clone
            }

            cardsToClone.forEach(card => {
                const clone = card.cloneNode(true);
                clone.classList.add('clone');
                scrollContainer.appendChild(clone);
            });
            hasCloned_carousel = true;
        }

        function cleanupInfiniteScroll_carousel() {
            const clones = scrollContainer.querySelectorAll('.clone');
            clones.forEach(clone => clone.remove());
            scrollContainer.scrollLeft = 0;
            hasCloned_carousel = false;
            if (animationFrameId_carousel) cancelAnimationFrame(animationFrameId_carousel);
        }

        scrollContainer.addEventListener('mouseenter', () => {
            isMouseHovering_carousel = true;
            if (animationFrameId_carousel) cancelAnimationFrame(animationFrameId_carousel);
        });
        scrollContainer.addEventListener('mouseleave', () => {
            isMouseHovering_carousel = false;
            if (isViewVisible_carousel) {
                animationFrameId_carousel = requestAnimationFrame(continuousScrollLoop_carousel);
            }
        });

        const viewObserver = new MutationObserver((mutationsList) => {
            for (const mutation of mutationsList) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    if (viewElement.style.display === 'block') {
                        isViewVisible_carousel = true;
                        prepareInfiniteScroll_carousel();
                        isMouseHovering_carousel = false;
                        if (animationFrameId_carousel) cancelAnimationFrame(animationFrameId_carousel);
                        animationFrameId_carousel = requestAnimationFrame(continuousScrollLoop_carousel);
                    } else {
                        if (isViewVisible_carousel) { // Only cleanup if it *was* visible
                           isViewVisible_carousel = false;
                           cleanupInfiniteScroll_carousel();
                        }
                    }
                }
            }
        });

        viewObserver.observe(viewElement, { attributes: true });

        // Return a cleanup function for explicit external cleanup if needed, though observer handles most cases
        return cleanupInfiniteScroll_carousel;
    }

    // Initialize carousels for each section
    const doctorCarouselCleanup = initializeCarousel('doctorBrowseView', 'doctorScrollContainer', '.doctor-card');
    const nurseCarouselCleanup = initializeCarousel('nurseBrowseView', 'nurseScrollContainer', '.doctor-card'); // Assuming nurses use .doctor-card style
    const homeServiceCarouselCleanup = initializeCarousel('homeServiceBrowseView', 'homeServiceScrollContainer', '.doctor-card'); // Assuming services use .doctor-card style

    // Call cleanup functions if the entire page is being left, though SPA-like behavior here
    // window.addEventListener('beforeunload', () => {
    //     if(doctorCarouselCleanup) doctorCarouselCleanup();
    //     if(nurseCarouselCleanup) nurseCarouselCleanup();
    //     if(homeServiceCarouselCleanup) homeServiceCarouselCleanup();
    // });


    // Initialize Firebase
    const firebaseConfig = {
        apiKey: "AIzaSyC62wo52XfJDOBnJc6VAQDDbse3a-KKy-k",
        authDomain: "dhipycare.firebaseapp.com",
        projectId: "dhipycare",
        storageBucket: "dhipycare.appspot.com", // Corrected from your provided firebasestorage.app
        messagingSenderId: "493427173597",
        appId: "1:493427173597:web:379ad40ef8360df81ad334",
        measurementId: "G-1R3S8TPCCV"
    };

    // Initialize Firebase
    if (typeof firebase !== 'undefined' && !firebase.apps.length) {
        firebase.initializeApp(firebaseConfig);
        console.log("Firebase initialized");
    } else if (typeof firebase !== 'undefined' && firebase.apps.length) {
        console.log("Firebase already initialized");
    } else {
        console.error("Firebase SDK not loaded!")
    }

    const db = (typeof firebase !== 'undefined' && firebase.firestore) ? firebase.firestore() : null;
    const auth = (typeof firebase !== 'undefined' && firebase.auth) ? firebase.auth() : null;

    // Function to fetch and display user data
    async function fetchAndDisplayUserData(userId) {
        if (!userId) { // If no userId (e.g., after logout)
            clearAccountPageFields();
            return;
        }
        if (!db) {
            console.error("Firestore not initialized");
            return;
        }
        try {
            const userDocRef = db.collection('users').doc(userId);
            const doc = await userDocRef.get();
            if (doc.exists) {
                const userData = doc.data();
                console.log("User data:", userData);
                originalUserData = { ...userData }; // Store for cancellation

                const currentAuthUser = auth.currentUser;
                const nameToDisplay = userData.fullName || (currentAuthUser ? currentAuthUser.displayName : null) || 'User';

                // Update Account Page details (display mode)
                accountPageUserName.textContent = nameToDisplay;
                accountEmail.textContent = userData.email || (currentAuthUser ? currentAuthUser.email : null) || 'N/A';
                accountPhone.textContent = userData.phoneNumber || userData.phone || 'N/A'; // Prioritize phoneNumber
                accountGender.textContent = userData.gender || 'N/A';
                accountDob.textContent = userData.dob || 'N/A';

                // Update input fields (for edit mode, but keep them hidden initially)
                accountPageUserNameInput.value = originalUserData.fullName || (auth.currentUser && auth.currentUser.displayName) || '';
                accountEmailInput.value = originalUserData.email || (auth.currentUser && auth.currentUser.email) || ''; // Email from auth or Firestore
                accountPhoneInput.value = originalUserData.phoneNumber || originalUserData.phone || ''; // Prioritize phoneNumber
                accountGenderInput.value = originalUserData.gender || 'male';
                accountDobInput.value = userData.dob || '';

                // Update sidebar username as well
                const headerUserNameElement = profileIconContainer.querySelector('span');
                if (headerUserNameElement) {
                    headerUserNameElement.textContent = nameToDisplay;
                }
                const sidebarUserNameElement = document.getElementById('sidebarUserName');
                if (sidebarUserNameElement) {
                    sidebarUserNameElement.textContent = nameToDisplay;
                }
            } else {
                console.log("No such document for user:", userId);
                // Fallback if no Firestore document - use auth display name or generic
                const currentAuthUser = auth.currentUser;
                const nameToDisplay = (currentAuthUser ? currentAuthUser.displayName : null) || 'User';

                accountPageUserName.textContent = nameToDisplay;
                accountEmail.textContent = (currentAuthUser ? currentAuthUser.email : null) || 'N/A';
                accountPhone.textContent = 'N/A'; // No phone here if no doc
                accountGender.textContent = 'N/A';
                accountDob.textContent = 'N/A';

                // Also update header/sidebar if no Firestore doc
                const headerUserNameElement = profileIconContainer.querySelector('span');
                if (headerUserNameElement) {
                    headerUserNameElement.textContent = nameToDisplay;
                }
                const sidebarUserNameElement = document.getElementById('sidebarUserName');
                if (sidebarUserNameElement) {
                    sidebarUserNameElement.textContent = nameToDisplay;
                }
            }
        } catch (error) {
            console.error("Error fetching user data: ", error);
        }
    }

    function toggleEditMode(editing) {
        isEditMode = editing;
        accountPage.classList.toggle('edit-mode', editing);

        // Toggle visibility of p vs input/select
        const displayElements = [accountPageUserName, accountEmail, accountPhone, accountGender, accountDob];
        const inputElements = [accountPageUserNameInput, accountEmailInput, accountPhoneInput, accountGenderInput, accountDobInput];

        displayElements.forEach(el => el.style.display = editing ? 'none' : 'block');
        inputElements.forEach(el => el.style.display = editing ? 'block' : 'none');

        // For username, the h3 and input are structured slightly differently
        accountPageUserName.style.display = editing ? 'none' : 'block';
        accountPageUserNameInput.style.display = editing ? 'block' : 'none';

        // Toggle buttons
        if (editProfileBtn) editProfileBtn.style.display = editing ? 'none' : 'block';
        if (saveProfileBtn) saveProfileBtn.style.display = editing ? 'block' : 'none';
        if (cancelEditBtn) cancelEditBtn.style.display = editing ? 'block' : 'none';

        if (editing) {
            // Populate inputs if entering edit mode (data should be fresh from fetchAndDisplayUserData)
            accountPageUserNameInput.value = originalUserData.fullName || (auth.currentUser && auth.currentUser.displayName) || '';
            accountEmailInput.value = originalUserData.email || (auth.currentUser && auth.currentUser.email) || ''; // Email from auth or Firestore
            accountPhoneInput.value = originalUserData.phoneNumber || originalUserData.phone || ''; // Prioritize phoneNumber
            accountGenderInput.value = originalUserData.gender || 'male';
            accountDobInput.value = originalUserData.dob || '';
        } else {
             // If exiting edit mode (e.g. after save or cancel), ensure display fields are updated
            fetchAndDisplayUserData(auth.currentUser.uid); // Re-fetch to show latest data
        }
    }

    if (editProfileBtn) {
        editProfileBtn.addEventListener('click', () => {
            toggleEditMode(true);
        });
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => {
            // Restore original values to display fields before toggling mode
            accountPageUserName.textContent = originalUserData.fullName || (auth.currentUser && auth.currentUser.displayName) || 'N/A';
            accountEmail.textContent = (auth.currentUser && auth.currentUser.email) || 'N/A';
            accountPhone.textContent = originalUserData.phoneNumber || originalUserData.phone || 'N/A'; // Prioritize phoneNumber
            accountGender.textContent = originalUserData.gender || 'N/A';
            accountDob.textContent = originalUserData.dob || 'N/A';
            toggleEditMode(false);
        });
    }

    if (saveProfileBtn) {
        saveProfileBtn.addEventListener('click', async () => {
            if (!auth || !auth.currentUser || !db) {
                alert("Error: Not logged in or Firebase not initialized.");
                return;
            }

            const updatedUserData = {
                fullName: accountPageUserNameInput.value.trim(),
                phoneNumber: accountPhoneInput.value.trim(), // Save as phoneNumber
                gender: accountGenderInput.value,
                dob: accountDobInput.value,
                // Email is typically not updated this way, as it's part of auth
                // If you need to update email, use Firebase Auth methods and handle verification
            };

            try {
                const userDocRef = db.collection('users').doc(auth.currentUser.uid);
                await userDocRef.update(updatedUserData);
                alert("Profile updated successfully!");
                originalUserData = { ...originalUserData, ...updatedUserData }; // Update local original data
                toggleEditMode(false); // This will re-fetch and display
            } catch (error) {
                console.error("Error updating profile: ", error);
                alert("Failed to update profile. See console for details.");
            }
        });
    }

    // Listen for auth state changes
    if (auth) {
        auth.onAuthStateChanged(user => {
            if (user) {
                console.log("User is logged in:", user.uid);
                // User is signed in, you can update UI or fetch initial data
                // For example, update the header username if it's static initially
                const headerUserNameElement = profileIconContainer.querySelector('span');
                if (headerUserNameElement) {
                    // Fetch name from Firestore for header on login
                    db.collection('users').doc(user.uid).get().then(doc => {
                        if (doc.exists) {
                            headerUserNameElement.textContent = doc.data().fullName || user.displayName || 'User'; // Changed 'name' to 'fullName'
                            document.getElementById('sidebarUserName').textContent = doc.data().fullName || user.displayName || 'User'; // Changed 'name' to 'fullName'
                        } else {
                            headerUserNameElement.textContent = user.displayName || 'User';
                            document.getElementById('sidebarUserName').textContent = user.displayName || 'User';
                        }
                    }).catch(err => {
                        console.error("Error fetching user name for header: ", err);
                        headerUserNameElement.textContent = user.displayName || 'User';
                        document.getElementById('sidebarUserName').textContent = user.displayName || 'User';
                    });
                } else {
                     document.getElementById('sidebarUserName').textContent = user.displayName || 'User';
                }
                // If user is logged in, you might want to pre-fetch their data or enable profile features
                fetchAndDisplayUserData(user.uid);
            } else {
                console.log("User is logged out");
                // User is signed out
                const headerUserNameElement = profileIconContainer.querySelector('span');
                if(headerUserNameElement) headerUserNameElement.textContent = 'Guest';
                document.getElementById('sidebarUserName').textContent = 'Guest';
                // Reset account page info if it was showing data
                showMainContent(); // Go to main view if user logs out while on account page
                toggleEditMode(false); // Ensure edit mode is off if user logs out, this calls fetchAndDisplayUserData(null)
                clearAccountPageFields(); // Explicitly clear fields when user is confirmed logged out
            }
        });
    }

    // Location Modal Logic
    const locationModal = document.getElementById('locationSelectionModal');
    const locationSearchInput = document.getElementById('locationSearchInput');
    const confirmLocationBtn = document.getElementById('confirmLocationBtn');
    const headerLocationText = document.querySelector('.location span');
    const useCurrentLocationBtn = document.getElementById('useCurrentLocationBtn');
    const selectedAddressDiv = document.getElementById('selectedAddress');
    const closeLocationModalBtn = document.getElementById('closeLocationModalBtn');
    const currentLocationStatusMsg = document.getElementById('currentLocationStatusMsg'); // Get the new status message element

    let googleMap;
    let googleMapMarker;
    let geocoder;
    let autocomplete;
    let chosenLocationDetails = null; // To store lat, lng, and formatted address

    // Helper function to format address for header
    function formatAddressForHeader(fullAddress) {
        if (!fullAddress) return "Select Location";

        const maxLength = 35; // Maximum characters to display on a single line

        if (fullAddress.length <= maxLength) {
            return fullAddress;
        }

        // Truncate and add ellipsis
        // Try to truncate at a space before maxLength for cleaner look
        let truncatedAddress = fullAddress.substring(0, maxLength);
        const lastSpaceIndex = truncatedAddress.lastIndexOf(' ');

        if (lastSpaceIndex > 0 && fullAddress.length > maxLength) { // Ensure we only truncate if needed and space exists
            truncatedAddress = truncatedAddress.substring(0, lastSpaceIndex);
        }

        return truncatedAddress + "...";
    }

    // This function will be called by the Google Maps API script when it loads
    window.initGoogleMap = function() {
        console.log("API CALLBACK: initGoogleMap function started.");
        const mapDiv = document.getElementById('map'); // Still useful for an early check

        if (!mapDiv) {
            console.error("API CALLBACK ERROR: Map container div (#map) not found. Map cannot be initialized.");
            return;
        }
        // Note: Map object creation and service initialization (geocoder, autocomplete)
        // are now handled by showLocationModal when it's first called.

        // Now that map is ready (or was already ready), check if we need to show the modal based on stored location
        if (!localStorage.getItem('userLocationSet')) {
            console.log("API CALLBACK: User location not set in localStorage. Calling showLocationModal() to potentially init map.");
            showLocationModal();
        } else {
            console.log("API CALLBACK: User location IS SET in localStorage. Modal should not show by default from API callback.");
            // If location is set, update header from localStorage (in case it wasn't done by DOMContentLoaded)
            const savedLocation = localStorage.getItem('userSelectedLocation');
            if (savedLocation && headerLocationText && headerLocationText.textContent !== savedLocation) {
                headerLocationText.textContent = formatAddressForHeader(savedLocation);
            }
        }
    };

    function placeMarkerAndPanTo(latLng, mapInstance) {
        if (googleMapMarker) {
            googleMapMarker.setMap(null); // Remove existing marker
        }
        googleMapMarker = new google.maps.Marker({
            position: latLng,
            map: mapInstance,
            // animation: google.maps.Animation.DROP // Optional nice animation
        });
        mapInstance.panTo(latLng);
        // mapInstance.setZoom(15); // Optionally adjust zoom after placing marker
    }

    function geocodeLatLng(latlng) { // Reverted signature
        geocoder.geocode({ location: latlng }, (results, status) => {
            if (status === "OK") {
                if (results[0]) {
                    locationSearchInput.value = results[0].formatted_address;
                    updateSelectedAddress(results[0].formatted_address, latlng);
                } else {
                    updateSelectedAddress(`Lat: ${latlng.lat().toFixed(5)}, Lng: ${latlng.lng().toFixed(5)}`, latlng);
                }
            } else {
                 updateSelectedAddress(`Geocoder failed. Lat: ${latlng.lat().toFixed(5)}, Lng: ${latlng.lng().toFixed(5)}`, latlng);
            }
            // Removed callback invocation
        });
    }

    function updateSelectedAddress(address, latLngObj) {
        selectedAddressDiv.textContent = address;
        chosenLocationDetails = {
            address: address,
            lat: latLngObj.lat(),
            lng: latLngObj.lng()
        };
    }

    function showLocationModal() {
        if (locationModal) {
            console.log("JS: showLocationModal() called."); // Log 7
            locationModal.style.display = 'flex';
            const mapDiv = document.getElementById('map');

            if (!mapDiv) {
                console.error("JS ERROR (showLocationModal): Map container div (#map) not found.");
                return;
            }

            // Initialize map and related services ONLY if they haven't been already
            if (!googleMap) {
                console.log("JS (showLocationModal): Initializing Google Map and services for the first time.");
                googleMap = new google.maps.Map(mapDiv, {
                    center: { lat: 26.8467, lng: 80.9462 }, // Default: Lucknow
                    zoom: 12,
                    mapTypeControl: false,
                    streetViewControl: false
                });

                geocoder = new google.maps.Geocoder();

                if (locationSearchInput) {
                    autocomplete = new google.maps.places.Autocomplete(locationSearchInput);
                    autocomplete.setFields(['address_components', 'geometry', 'icon', 'name', 'formatted_address']);

                    autocomplete.addListener('place_changed', () => {
                        const place = autocomplete.getPlace();
                        if (!place.geometry || !place.geometry.location) {
                            selectedAddressDiv.textContent = "Please select a valid location from the suggestions.";
                            chosenLocationDetails = null;
                            return;
                        }

                        if (place.geometry.viewport) {
                            googleMap.fitBounds(place.geometry.viewport);
                        } else {
                            googleMap.setCenter(place.geometry.location);
                            googleMap.setZoom(15);
                        }
                        placeMarkerAndPanTo(place.geometry.location, googleMap);
                        locationSearchInput.value = place.formatted_address;
                        updateSelectedAddress(place.formatted_address, place.geometry.location);
                    });
                } else {
                    console.warn("JS (showLocationModal): locationSearchInput not found for Autocomplete setup.");
                }

                googleMap.addListener('click', (mapsMouseEvent) => {
                    const latLng = mapsMouseEvent.latLng;
                    placeMarkerAndPanTo(latLng, googleMap);
                    geocodeLatLng(latLng);
                });
                console.log("JS (showLocationModal): Google Map and services initialized.");

            } else {
                // Map already exists, just ensure it's sized correctly and centered
                console.log("JS (showLocationModal): googleMap exists. Triggering resize.");
                google.maps.event.trigger(googleMap, 'resize');
                const currentCenter = googleMap.getCenter();
                if (currentCenter) {
                    googleMap.setCenter(currentCenter); // Restore previous center
                } else {
                    googleMap.setCenter({ lat: 26.8467, lng: 80.9462 }); // Default center
                }
                // Consider if zoom should be restored or reset:
                // googleMap.setZoom(12); // Or restore a saved zoom level
            }
        } else {
            console.error("JS ERROR (showLocationModal): locationModal element not found.");
        }
    }

    function hideLocationModal() {
        if (locationModal) locationModal.style.display = 'none';
    }

    if (confirmLocationBtn) {
        confirmLocationBtn.addEventListener('click', () => {
            // const selectedLocation = locationSearchInput.value.trim(); // Keep using this for manual input if desired
            if (chosenLocationDetails && chosenLocationDetails.address) {
                if (headerLocationText) {
                    headerLocationText.textContent = formatAddressForHeader(chosenLocationDetails.address);
                }
                localStorage.setItem('userLocationSet', 'true');
                localStorage.setItem('userSelectedLocation', chosenLocationDetails.address); // Store full address
                localStorage.setItem('userSelectedLat', chosenLocationDetails.lat);
                localStorage.setItem('userSelectedLng', chosenLocationDetails.lng);
                console.log("Location selected and saved:", chosenLocationDetails);
                hideLocationModal();
            } else if (locationSearchInput.value.trim()){
                // Fallback if user typed something but didn't select from autocomplete or click map
                // Attempt to geocode the input text to get lat/lng for storage
                geocoder.geocode({ address: locationSearchInput.value.trim() }, (results, status) => {
                    if (status === 'OK' && results[0]) {
                        const loc = results[0].geometry.location;
                        chosenLocationDetails = { address: results[0].formatted_address, lat: loc.lat(), lng: loc.lng() };
                        if (headerLocationText) headerLocationText.textContent = formatAddressForHeader(chosenLocationDetails.address);
                        localStorage.setItem('userLocationSet', 'true');
                        localStorage.setItem('userSelectedLocation', chosenLocationDetails.address); // Store full address
                        localStorage.setItem('userSelectedLat', chosenLocationDetails.lat);
                        localStorage.setItem('userSelectedLng', chosenLocationDetails.lng);
                        hideLocationModal();
                    } else {
                        alert("Please select a valid location from the suggestions, click the map, or use current location.");
                    }
                });
            } else {
                alert("Please select or type a location.");
            }
        });
    }

    if (useCurrentLocationBtn) {
        useCurrentLocationBtn.addEventListener('click', () => {
            if (navigator.geolocation) {
                currentLocationStatusMsg.textContent = 'Please wait Fetching your location...'; // Updated text
                currentLocationStatusMsg.style.display = 'block'; // Show message

                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        currentLocationStatusMsg.style.display = 'none'; // Hide message
                        const pos = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude,
                        };
                        if (googleMap && geocoder) {
                            placeMarkerAndPanTo(pos, googleMap);
                            geocodeLatLng(pos); // Reverted call
                            googleMap.setZoom(15);
                        } else {
                            alert("Map or geocoding service not ready yet. Please try again shortly.");
                        }
                    },
                    (error) => {
                        currentLocationStatusMsg.style.display = 'none'; // Hide message on error too
                        let message = "Error getting current location.";
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                message = "Location permission denied. Please allow location access for this site in your browser and device settings.";
                                break;
                            case error.POSITION_UNAVAILABLE:
                                message = "Could not get current location. Please ensure your device's GPS or location service is turned ON and try again. If you are indoors, try moving to a location with a clearer view of the sky.";
                                break;
                            case error.TIMEOUT:
                                message = "Finding your location timed out. Please check your connection, ensure GPS is on, and try again.";
                                break;
                            default:
                                message = "Unknown error getting location: " + error.message;
                        }
                        alert(message); // Re-instating alert with more descriptive messages
                    },
                    { timeout: 20000, enableHighAccuracy: true } // Options object
                );
            } else {
                currentLocationStatusMsg.style.display = 'none'; // Hide message if geolocation not supported
                alert("Geolocation not supported by this browser.");
            }
        });
    }

    if (closeLocationModalBtn) {
        closeLocationModalBtn.addEventListener('click', hideLocationModal);
    }

    // Add event listener for the header location display
    const headerLocationDisplay = document.querySelector('header .location');
    if (headerLocationDisplay) {
        headerLocationDisplay.addEventListener('click', () => {
            console.log("Header location display clicked. Attempting to show location modal.");
            showLocationModal();
            // initGoogleMap will be called by the API script.
            // If it has already run, showLocationModal will resize the existing map.
            // If it hasn't run, showLocationModal displays the modal, and initGoogleMap will populate it.
        });
    }

    // Show location modal on page load - adjusted for Google Maps init
    if (!localStorage.getItem('userLocationSet')) {
        console.log("DOM READY: User location not set in localStorage. initGoogleMap callback will handle showing modal."); // Log 9
    } else {
        const savedLocation = localStorage.getItem('userSelectedLocation');
        if (savedLocation && headerLocationText) {
            headerLocationText.textContent = formatAddressForHeader(savedLocation);
            console.log("DOM READY: User location IS SET. Loaded from localStorage: ", savedLocation); // Log 10
        }
    }
    // Make initGoogleMap globally accessible for the API callback - MOVED to be defined directly on window
    // window.initGoogleMap = initGoogleMap;

    // When DOM is ready, if location not set, show modal. initGoogleMap will be called by API.
    // If location IS set, initGoogleMap won't be called unless modal is manually opened.
    // This ensures map only initializes if modal is intended to be shown.
    // However, the Google Maps API script *must* be loaded for any of this to work.
    // The `callback=initGoogleMap` in the API script URL is key.
    // The initGoogleMap function will run once the API script is loaded.
    // We then need to decide if the modal should be shown by default in initGoogleMap.

    // --- Health Issue Form Logic ---
    const healthIssueFormView = document.getElementById('healthIssueFormView');
    const healthIssueForm = document.getElementById('healthIssueForm');
    const backToBrowseViewBtn = document.getElementById('backToBrowseViewBtn');
    const patientAddressInput = document.getElementById('patientAddress');
    const healthIssueFormTitle = document.getElementById('healthIssueFormTitle');
    let lastBrowseView = null;

    // Show form when any concern-item is clicked
    document.querySelectorAll('.concern-item').forEach(item => {
        item.addEventListener('click', function() {
            const issue = item.querySelector('p') ? item.querySelector('p').textContent : '';
            if (issue) {
                window.location.href = `healthform.html?issue=${encodeURIComponent(issue)}`;
            } else {
                window.location.href = 'healthform.html';
            }
        });
    });

    // Back button logic
    if (backToBrowseViewBtn) {
        backToBrowseViewBtn.addEventListener('click', function() {
            if (healthIssueFormView) healthIssueFormView.style.display = 'none';
            if (lastBrowseView) lastBrowseView.style.display = 'block';
        });
    }

    // (Optional) Handle form submit
    if (healthIssueForm) {
        healthIssueForm.addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Form submitted! (You can handle the data as needed)');
            // Optionally, reset form and go back
            healthIssueForm.reset();
            if (healthIssueFormView) healthIssueFormView.style.display = 'none';
            if (lastBrowseView) lastBrowseView.style.display = 'block';
        });
    }

    // JavaScript code for new carousel banners
    const track = document.querySelector('.carousel-track');
    const banners = Array.from(track.children);

    if (banners.length > 0) {
        let bannerWidth;
        let currentIndex = 0;
        const actualBannersCount = banners.length - 1; // Account for the clone

        function initializeCarousel() {
            if (!banners[0]) {
                console.error("DhipyCare Carousel: Banners not found for initialization.");
                return;
            }
            // Attempt to get width, prefer getBoundingClientRect but fallback to offsetWidth
            bannerWidth = banners[0].getBoundingClientRect().width;
            if (!bannerWidth || bannerWidth === 0) {
                bannerWidth = banners[0].offsetWidth;
            }

            console.log("DhipyCare Carousel: Initializing with Banner width:", bannerWidth);

            if (!bannerWidth || bannerWidth === 0) {
                console.error("DhipyCare Carousel ERROR: Calculated banner width is 0. Auto-swipe may not work as expected. Ensure carousel container and banners are visible and have dimensions when this script runs.");
                // Optionally, you could try to re-initialize after a short delay if width is 0
                // setTimeout(initializeCarousel, 500); // Example: try again in 0.5s
                return; // Stop if width is 0 to prevent errors, or let it proceed if manual nav is still desired
            }
            updateSlidePosition(true); // true to disable transition for initial setup
        }

        function updateSlidePosition(disableTransition = false) {
            if (!bannerWidth || bannerWidth === 0) {
                 // Re-calculate width if it's somehow zero (e.g., if called before full load/display)
                if (banners.length > 0 && banners[0]) {
                    bannerWidth = banners[0].getBoundingClientRect().width || banners[0].offsetWidth;
                }
                if (!bannerWidth || bannerWidth === 0) {
                    console.error("DhipyCare Carousel (updateSlidePosition): Banner width is 0. Cannot update position.");
                    return;
                }
            }

            if (disableTransition) {
                track.style.transition = 'none';
            }
            track.style.transform = 'translateX(-' + bannerWidth * currentIndex + 'px)';
            if (disableTransition) {
                // Force a reflow before re-enabling transitions for the initial jump
                void track.offsetWidth; // Reading offsetWidth can trigger a reflow
                track.style.transition = 'transform 0.5s ease-in-out';
            }
        }

        function moveToNextSlide() {
            currentIndex++;
            updateSlidePosition();

            // Check if we've reached the cloned slide
            if (currentIndex >= actualBannersCount) {
                // After the transition to the clone finishes, jump back to the start
                setTimeout(() => {
                    currentIndex = 0;
                    updateSlidePosition(true); // true to disable transition for the jump
                }, 500); // Match transition duration
            }
        }

        function moveToPrevSlide() {
            if (currentIndex === 0) {
                // We are at the first slide, jump to the clone at the end (which is visually the same as the last actual slide)
                currentIndex = actualBannersCount; // Index of the clone
                updateSlidePosition(true); // Disable transition for the jump

                // Then, immediately (or after a tiny delay) animate to the *actual* last slide
                setTimeout(() => {
                     currentIndex = actualBannersCount - 1; // Index of the actual last slide
                     if(currentIndex < 0) currentIndex = 0; // Safety for very few slides
                     updateSlidePosition(); // Animate to it
                }, 20); // Small delay
            } else {
                currentIndex--;
                updateSlidePosition();
            }
        }

        window.moveBannerManually = function(direction) {
            if (direction === 1) { moveToNextSlide(); }
            else if (direction === -1) { moveToPrevSlide(); }
            resetSlideshowTimer();
        };

        let slideshowInterval = setInterval(moveToNextSlide, 3000); // Autoplay interval
        function resetSlideshowTimer() {
            clearInterval(slideshowInterval);
            slideshowInterval = setInterval(moveToNextSlide, 3000);
        }

        // Initialize on window load to ensure all elements are rendered and have dimensions
        window.addEventListener("load", initializeCarousel);

        // Handle window resize
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                // Re-initialize carousel on resize to get new widths
                initializeCarousel();
            }, 250);
        });

    } else {
        console.error("DhipyCare Carousel: Carousel track or banners not found.");
    }
});
    </script>
    <!-- Firebase SDKs -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <!-- Leaflet JS REMOVED -->
    <!-- Google Maps API Script (ensure only one instance) -->
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC62wo52XfJDOBnJc6VAQDDbse3a-KKy-k&libraries=places,geocoding&callback=initGoogleMap"></script>

    <div class="delivery-box">
        <div class="delivery-icon-wrapper">
            <img src="https://via.placeholder.com/60x60/e0f2f7/000000?text=Icon" alt="Delivery Icon" class="delivery-icon-img">
            </div>
        <p class="delivery-text">
            <span class="highlight-red">30 MINS</span> at Your Door
        </p>
    </div>

</body>
</html>


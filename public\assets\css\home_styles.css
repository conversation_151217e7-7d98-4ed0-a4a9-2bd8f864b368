/* General Body Styles (if not already present, good for context) */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  background-color: #f4f7f6; /* Light background for the main content area */
  color: #333;
  overflow-x: hidden; /* Prevent horizontal scroll issues with sidebar */
}

/* Sidebar Styles */
.sidebar {
  height: 100%;
  width: 0; /* Initially hidden */
  position: fixed;
  z-index: 1001; /* Higher than sticky header (often 1000) */
  top: 0;
  left: 0;
  background-color: #ffffff; /* Clean white background */
  overflow-x: hidden;
  transition: 0.3s ease-in-out; /* Smooth transition for open/close */
  box-shadow: 2px 0 10px rgba(0,0,0,0.1); /* Subtle shadow */
  display: flex;
  flex-direction: column;
}

.sidebar.open {
  width: 280px; /* Width when open */
}

.sidebar-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.close-sidebar {
  position: absolute;
  top: 15px;
  right: 25px;
  font-size: 36px;
  font-weight: bold;
  color: #555;
  cursor: pointer;
  transition: color 0.2s;
}

.close-sidebar:hover {
  color: #1a73e8; /* DhipyCare blue or a primary color */
}

.sidebar-content h3 { /* "Profile" heading */
  margin-top: 40px; /* Space below close button */
  margin-bottom: 15px;
  font-size: 1.4em;
  color: #223a7a; /* DhipyCare primary dark blue */
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

#sidebar-profile-details {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding: 10px;
  background-color: #f0f2f5; /* Light grey background for profile section */
  border-radius: 8px;
}

#sidebar-profile-photo {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #ccc; /* Placeholder background */
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden; /* Ensure images fit */
}

#sidebar-profile-photo img,
#sidebar-profile-photo svg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

#sidebar-profile-name {
  font-size: 1.1em;
  font-weight: 600;
  color: #333;
}

.sidebar-content ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  flex-grow: 1; /* Allows logout to be pushed down if needed */
}

.sidebar-content ul li {
  padding: 12px 15px;
  text-decoration: none;
  font-size: 1em;
  color: #444;
  display: block;
  transition: background-color 0.2s, color 0.2s;
  cursor: pointer;
  border-radius: 6px;
  margin-bottom: 5px;
}

.sidebar-content ul li:hover {
  background-color: #e9f5ff; /* Light blue hover */
  color: #1a73e8; /* DhipyCare blue */
}

.sidebar-content ul li#logout-btn {
  margin-top: auto; /* Pushes logout to the bottom */
  border-top: 1px solid #eee;
  padding-top: 15px;
  color: #d9534f; /* A reddish color for logout */
}

.sidebar-content ul li#logout-btn:hover {
  background-color: #fbeae9; /* Light red hover */
  color: #c9302c;
}

/* Overlay for when sidebar is open */
#sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.4);
  z-index: 1000; /* Below sidebar, above main content */
  transition: opacity 0.3s ease-in-out;
  opacity: 0;
}

#sidebar-overlay.active {
  display: block;
  opacity: 1;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .sidebar.open {
    width: 250px; /* Slightly smaller for smaller screens */
  }
  .sidebar-content h3 {
    font-size: 1.3em;
  }
  #sidebar-profile-name {
    font-size: 1em;
  }
  .sidebar-content ul li {
    font-size: 0.95em;
    padding: 10px 12px;
  }
}

/* Other Styles that were in home_styles.css or should be */
.modern-header {
  background: linear-gradient(90deg, #1976D2 0%, #3ec6e0 100%);
  color: white;
  padding: 10px 15px;
  position: sticky;
  top: 0;
  z-index: 1000; 
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.modern-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}
.header-profile-section {
    display: flex;
    align-items: center;
}
.profile-icon {
  cursor: pointer;
  margin-right: 8px;
}
#header-profile-name {
  font-weight: 500;
  font-size: 0.95em;
}
.modern-location {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
  background-color: rgba(255,255,255,0.1);
}
.modern-location:hover {
  background-color: rgba(255,255,255,0.2);
}
.location-icon {
  margin-right: 6px;
  font-size: 1.1em;
}
.modern-location-text {
  font-size: 0.9em;
  font-weight: 500;
  margin-right: 4px;
}
.chevron {
  font-size: 0.7em;
}

.modern-horizontal-cards {
    display: flex;
    justify-content: space-around; 
    padding: 15px;
    gap: 15px; 
    flex-wrap: wrap; 
    background-color: #fff; 
    border-radius: 0 0 12px 12px; 
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    margin-bottom: 15px; 
}

.modern-h-card {
    flex-basis: calc(33.333% - 20px); 
    min-width: 280px; 
    background: linear-gradient(135deg, #f5f7fa 0%, #e0e8f0 100%);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
    color: #333;
}

.modern-h-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.modern-h-card-img {
    width: 60px; 
    height: 60px;
    border-radius: 8px; 
    margin-right: 15px;
    object-fit: cover;
}

.modern-h-card-label {
    font-size: 1.1em;
    font-weight: 600;
    color: #223a7a; 
    line-height: 1.3;
}

.modern-h-card-arrow {
    margin-left: auto; 
    font-size: 1.5em;
    font-weight: bold;
    color: #1976D2; 
}

.health-issues-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    padding: 15px;
}
.health-issue-item {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: box-shadow 0.2s, transform 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}
.health-issue-item:hover {
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    transform: translateY(-2px);
    border-color: #1976D2;
}
.health-issue-icon-placeholder {
    width: 40px;
    height: 40px;
    background-color: #e9f5ff; 
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    color: #1976D2;
    font-weight: bold;
    font-size: 0.9em; 
}
.health-issue-item span {
    font-size: 0.95em;
    color: #333;
    font-weight: 500;
}

.scrolling-wrapper-container {
  overflow: hidden; 
  padding: 10px 0;
  margin-bottom: 15px; 
  position: relative; 
}
.scrolling-wrapper {
  display: flex;
  overflow-x: auto; 
  -webkit-overflow-scrolling: touch; 
  padding-bottom: 15px; 
  gap: 15px; 
  width: max-content; 
}

.scrolling-wrapper::-webkit-scrollbar {
  height: 8px; 
}
.scrolling-wrapper::-webkit-scrollbar-track {
  background: #f0f2f5; 
  border-radius: 4px;
}
.scrolling-wrapper::-webkit-scrollbar-thumb {
  background: #1976D2; 
  border-radius: 4px;
  transition: background-color 0.2s;
}
.scrolling-wrapper::-webkit-scrollbar-thumb:hover {
  background: #145a9e; 
}

.scrolling-wrapper {
  scrollbar-width: thin; 
  scrollbar-color: #1976D2 #f0f2f5; 
}

.doctor-scroll-item, .nurse-scroll-item, .assistant-scroll-item {
  flex: 0 0 auto; 
  width: 150px; 
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 8px rgba(0,0,0,0.07);
  padding: 12px;
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
}
.doctor-scroll-item:hover, .nurse-scroll-item:hover, .assistant-scroll-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(0,0,0,0.1);
}
.doctor-scroll-item img, .nurse-scroll-item img, .assistant-scroll-item img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 8px;
  border: 2px solid #e0e8f0;
}
.doctor-name, .nurse-name, .assistant-name {
  display: block;
  font-weight: 600;
  color: #223a7a;
  font-size: 1em;
  margin-bottom: 4px;
}
.doctor-description, .nurse-description, .assistant-info {
  font-size: 0.85em;
  color: #555;
  line-height: 1.3;
}
#summary-assistant-scroller .scrolling-wrapper { 
    animation: none;
    width: auto; 
}
#summary-doctor-preview-scroller .scrolling-wrapper { 
    animation: none;
    width: auto; 
}

#consultation-summary-section { display: none; background-color: #f4f7f6; min-height: 100vh; }
.summary-header {
    background: linear-gradient(90deg, #1976D2 0%, #3ec6e0 100%);
    color: white;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.summary-header button { 
    background: none; border: none; color: white; font-size: 1.8em; cursor: pointer; margin-right: 15px; padding: 0 5px;
}
.summary-header h2 { margin: 0; font-size: 1.2em; font-weight: 600; }
.summary-content { padding: 15px; max-width: 700px; margin: 0 auto; }

.summary-consultation-title-box {
    background-color: #e9f5ff; 
    padding: 15px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    border: 1px solid #cce7ff;
}
.summary-consultation-title-box h3 {
    margin: 0; color: #145a9e; 
    font-size: 1.2em; font-weight: 600;
}
.summary-consultation-icon img { width: 40px; height: 40px; border-radius: 50%; }

.summary-assignment-text { text-align: center; margin-bottom: 20px; }
.summary-assignment-text p { font-size: 1.1em; font-weight: bold; color: #223a7a; margin-bottom: 2px; }
.summary-assignment-text span { font-size: 0.9em; color: #555; }

#summary-assistant-scroller-title {
  font-size: 1.1em;
  color: #223a7a;
  margin-top: 20px;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ddd;
}

#patient-consultation-form h3 {
    font-size: 1.2em; color: #223a7a; margin-top: 20px; margin-bottom: 15px;
    padding-bottom: 8px; border-bottom: 1px solid #ddd;
}
#patient-consultation-form .form-group { margin-bottom: 15px; }
#patient-consultation-form label {
    display: block; font-size: 0.95em; color: #444; font-weight: 500; margin-bottom: 5px;
}
#patient-consultation-form input[type="text"],
#patient-consultation-form input[type="number"],
#patient-consultation-form input[type="tel"],
#patient-consultation-form select,
#patient-consultation-form textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ccc;
    border-radius: 6px;
    box-sizing: border-box;
    font-size: 1em;
    transition: border-color 0.2s;
}
#patient-consultation-form input:focus,
#patient-consultation-form select:focus,
#patient-consultation-form textarea:focus {
    border-color: #1976D2;
    outline: none;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}
.address-form-row { display: flex; gap: 15px; }
.address-form-row .form-group { flex: 1; }

.address-type-selector { margin-top: 15px; margin-bottom: 20px; }
.address-type-selector label:first-child { 
    display: block; font-size: 0.95em; color: #444; font-weight: 500; margin-bottom: 8px;
}
.address-type-buttons { display: flex; gap: 10px; }
.address-type-btn {
    padding: 8px 15px; border: 1px solid #ccc; border-radius: 20px; cursor: pointer;
    font-size: 0.9em; color: #555; transition: all 0.2s;
}
.address-type-btn input[type="radio"] { display: none; } 
.address-type-btn.selected {
    background-color: #1976D2; color: white; border-color: #1976D2; font-weight: 500;
}

#summary-continue-btn, #summary-continue-to-payment-btn { 
    display: block; width: 100%; background: linear-gradient(90deg, #ff416c, #ff4b2b);
    color: white; border: none; padding: 12px; border-radius: 8px; font-size: 1.1em;
    font-weight: bold; cursor: pointer; text-align: center; margin-top: 20px;
    transition: opacity 0.2s;
}
#summary-continue-btn:hover, #summary-continue-to-payment-btn:hover { opacity: 0.9; }
#summary-continue-to-payment-btn { display: none; } 

#payment-selection-section {
    display: none; 
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    margin-top: 20px;
}
#payment-selection-section h3 {
    text-align: center; color: #223a7a; font-size: 1.3em; margin-bottom: 10px;
}
#confirm-booking-btn {
    display: block; width: 100%; background: linear-gradient(90deg, #28a745, #218838); 
    color: white; border: none; padding: 12px; border-radius: 8px; font-size: 1.1em;
    font-weight: bold; cursor: pointer; text-align: center; margin-top: 15px;
    transition: opacity 0.2s;
}
#confirm-booking-btn:disabled { background: #ccc; cursor: not-allowed; }
#confirm-booking-btn:hover:not(:disabled) { opacity: 0.9; }

#booking-confirmation-message {
    margin-top: 20px; padding: 15px; border-radius: 8px;
    font-size: 1.05em; text-align: center; font-weight: 500;
}

.location-overlay {
    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex; align-items: center; justify-content: center;
    z-index: 2000; 
    opacity: 0; transition: opacity 0.2s ease-in-out;
}
.location-overlay.active { opacity: 1; }
.location-modal {
    background-color: white; padding: 25px; border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    width: 90%; max-width: 500px;
    transform: scale(0.95); transition: transform 0.2s ease-in-out;
    position: relative;
}
.location-overlay.active .location-modal { transform: scale(1); }
.close-location-modal {
    position: absolute; top: 10px; right: 15px; font-size: 28px;
    font-weight: bold; color: #777; cursor: pointer; transition: color 0.2s;
}
.close-location-modal:hover { color: #333; }
.location-modal h2 {
    color: #223a7a; font-size: 1.5em; margin-top: 0; margin-bottom: 20px; text-align: center;
}
.location-modal input[type="text"] {
    width: 100%; padding: 12px; margin-bottom: 15px;
    border: 1px solid #ccc; border-radius: 6px; box-sizing: border-box; font-size: 1em;
}
.location-modal button {
    width: 100%; padding: 12px; border-radius: 6px; border: none;
    font-size: 1em; font-weight: bold; cursor: pointer; transition: background-color 0.2s;
}
#use-current-location {
    background-color: #f0f0f0; color: #333; margin-bottom: 15px;
}
#use-current-location:hover { background-color: #e0e0e0; }
#location-submit {
    background: linear-gradient(90deg, #1976D2, #3ec6e0); color: white;
}
#location-submit:disabled { background: #ccc; cursor: not-allowed; }
#location-submit:hover:not(:disabled) { opacity:0.9; }
.location-error {
    color: #dc3545; font-size: 0.9em; text-align: center; margin-top: 10px; min-height: 1.2em;
}
#map { margin-bottom: 15px !important; } 
#selected-address { font-weight: 500; }

.profile-card-view {
    background-color: #fff; padding: 20px; border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08); max-width: 600px; margin: 10px auto;
}
.profile-photo-container {
    width: 100px; height: 100px; border-radius: 50%; background-color: #e0e8f0;
    margin: 0 auto 15px auto; display: flex; align-items: center; justify-content: center;
    overflow: hidden; border: 3px solid #fff; box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
.profile-photo-container img, .profile-photo-container svg {
    width: 100%; height: 100%; object-fit: cover;
}
.profile-name-title {
    text-align: center; color: #223a7a; font-size: 1.6em; margin-bottom: 20px;
}
.profile-details-grid { display: grid; gap: 12px; margin-bottom: 25px; }
.detail-item {
    display: flex; justify-content: space-between; padding: 8px 0;
    border-bottom: 1px solid #f0f2f5; font-size: 1em;
}
.detail-item:last-child { border-bottom: none; }
.detail-label { font-weight: 500; color: #555; }
.detail-value { color: #333; text-align: right; }
.profile-edit-button, .profile-save-button, .profile-cancel-button {
    display: block; width: auto; min-width: 120px; padding: 10px 25px; margin: 10px auto 0 auto;
    border-radius: 25px; font-weight: bold; cursor: pointer; text-align: center;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    border: none;
}
.profile-edit-button {
  background: linear-gradient(90deg, #1976D2, #3ec6e0); color: white;
}
.profile-edit-button:hover { opacity: 0.9; box-shadow: 0 2px 8px rgba(25,118,210,0.3); }

#booking-history-container { display: grid; gap: 15px; }
.booking-item-card {
    background-color: #fff; border-radius: 8px; padding: 15px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.07);
    border-left: 5px solid #1976D2; 
}
.booking-item-card h3 { margin-top: 0; margin-bottom: 8px; font-size: 1.15em; color: #223a7a;}
.booking-item-card p { margin: 5px 0; font-size: 0.95em; color: #444; }
.booking-item-card p strong { color: #333; }
.booking-status { font-weight: bold; padding: 3px 8px; border-radius: 4px; font-size: 0.9em; color: white; }
.booking-status-confirmed { background-color: #28a745; 
}
.booking-status-completed { background-color: #17a2b8; 
}
.booking-status-cancelled { background-color: #dc3545; 
}
.booking-status-pending { background-color: #ffc107; color: #333; 
}
.booking-item-card.status-completed { border-left-color: #17a2b8; }
.booking-item-card.status-cancelled { border-left-color: #dc3545; }
.booking-item-card.status-pending { border-left-color: #ffc107; }

#doctor-flowchart-popup {
    display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%;
    background-color: rgba(0,0,0,0.6); z-index: 1002; 
    align-items: center; justify-content: center; padding: 20px; box-sizing: border-box;
}
.flowchart-popup-content {
    background-color: #fff; padding: 25px; border-radius: 10px;
    max-width: 700px; width: 95%; max-height: 90vh; overflow-y: auto;
    position: relative; box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}
.flowchart-popup-close {
    position: absolute; top: 10px; right: 15px; font-size: 2em; font-weight: bold;
    color: #777; background: none; border: none; cursor: pointer;
}
.flowchart-popup-content h2 { text-align: center; color: #223a7a; margin-top: 0; margin-bottom: 20px; }
.flowchart-step { margin-bottom: 20px; padding-bottom:15px; border-bottom: 1px dashed #eee; }
.flowchart-step:last-child { border-bottom: none; margin-bottom: 0; }
.flowchart-step h3 { color: #1976D2; font-size: 1.15em; margin-bottom: 8px; }
.flowchart-step p { font-size: 0.95em; line-height: 1.6; color: #555; margin-bottom: 8px; }
.flowchart-illustration-placeholder {
    font-style: italic; color: #888; text-align: center; font-size: 0.9em;
    padding: 10px; background-color: #f9f9f9; border-radius: 5px;
}
.doctor-flow-navigation {
    display: flex; justify-content: space-between; align-items: center;
    padding: 15px; margin-top: 10px; border-top: 1px solid #eee;
}
.doctor-flow-navigation button {
    padding: 10px 20px; border-radius: 6px; border: none; cursor: pointer;
    font-weight: bold; font-size: 0.95em;
}
.doctor-flow-navigation #skip-health-issues-btn {
    background-color: #1976D2; color: white;
}
 .doctor-flow-navigation .doctor-flow-back-btn {
    background-color: #6c757d; color: white;
}

#health-issues-section, #horizontal-doctor-scroll-section, #horizontal-nurse-scroll-section, #services-overview-section {
    padding: 15px; background-color: #fff; margin-bottom: 10px; border-radius:8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}
#health-issues-section h2, #horizontal-doctor-scroll-section h2, #horizontal-nurse-scroll-section h2, .section-title {
  text-align: center; color: #223a7a; margin-top:5px; margin-bottom: 20px; font-size: 1.5em;
}

.faq-list { max-width: 700px; margin: 0 auto 20px auto; }
.faq-item {
    background-color: #fff; border: 1px solid #e0e0e0;
    border-radius: 6px; margin-bottom: 10px; overflow: hidden;
}
.faq-question {
    width: 100%; background-color: transparent; border: none;
    padding: 15px; text-align: left; font-size: 1.05em; font-weight: 500;
    color: #333; cursor: pointer; display: flex; justify-content: space-between;
    align-items: center;
}
.faq-question::after { 
    content: '\276F'; 
    font-size: 1.2em; color: #1976D2;
    transition: transform 0.3s ease;
}
.faq-item.active .faq-question::after { transform: rotate(90deg); }
.faq-answer {
    padding: 0 15px; max-height: 0; overflow: hidden;
    font-size: 0.95em; color: #555; line-height: 1.6;
    transition: max-height 0.3s ease, padding 0.3s ease;
}
.faq-item.active .faq-answer { max-height: 200px; 
padding-bottom: 15px; }

.services-list { display: grid; gap: 15px; margin-bottom: 25px; }
@media (min-width: 600px) { .services-list { grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); } }
.service-info-card {
    background-color: #f9f9f9; border-radius: 8px; padding: 15px;
    display: flex; align-items: flex-start; border: 1px solid #eee;
}
.service-info-img {
    width: 50px; height: 50px; border-radius: 6px; margin-right: 15px; object-fit: cover;
}
.service-info-main { flex: 1; }
.service-info-title { display: block; font-weight: 600; color: #1976D2; margin-bottom: 8px; font-size: 1.1em; }
.service-bullets { list-style: none; padding-left: 0; margin: 0; }
.service-bullets li {
    font-size: 0.9em; color: #666; margin-bottom: 4px;
    position: relative; padding-left: 15px;
}
.service-bullets li::before {
    content: '\2713'; 
    color: #28a745;
    position: absolute; left: 0; font-size: 0.9em;
}

#my-account-section > #back-to-main-content-btn,
#my-bookings-section > #back-to-main-from-bookings-btn,
#terms-conditions-section > #back-to-main-from-terms-btn,
#contact-us-section > #back-to-main-from-contact-btn {
  display: inline-block; 
  margin-bottom:15px; padding:8px 16px; border-radius:20px;
  border:1px solid #1976D2; background:#fff; color:#1976D2;
  font-weight:600; cursor:pointer; transition: all 0.2s;
}
#my-account-section > #back-to-main-content-btn:hover,
#my-bookings-section > #back-to-main-from-bookings-btn:hover,
#terms-conditions-section > #back-to-main-from-terms-btn:hover,
#contact-us-section > #back-to-main-from-contact-btn:hover {
    background:#e9f5ff; color:#145a9e;
}

.main-content {
  padding-top: 60px; 
}

@media (max-width: 768px) {
  .modern-header { padding: 8px 10px; }
  .header-profile-section { 
  flex-shrink: 0; }
  #header-profile-name { font-size: 0.9em; max-width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
  .modern-location { padding: 6px 8px; }
  .modern-location-text { font-size: 0.85em; }
  .location-icon { font-size: 1em; }
  .chevron { font-size: 0.6em; }

  .modern-horizontal-cards { flex-direction: column; padding: 10px; gap:10px; }
  .modern-h-card { min-width: unset; width:100%; flex-basis: auto; }
  .modern-h-card-label { font-size: 1em; }
  
  .main-content { padding-top: 50px; 
  }
  
  .profile-card-view { padding: 15px; }
  .profile-name-title { font-size: 1.4em; }
  .detail-item { font-size: 0.95em; }

  .address-form-row { flex-direction: column; gap: 0; } 
  .address-form-row .form-group { margin-bottom: 15px; }
  .address-form-row .form-group:last-child { margin-bottom: 0; }
  
  #consultation-summary-section .summary-header h2 { font-size: 1.1em; }
  #consultation-summary-section .summary-content { padding: 10px; }
  .summary-consultation-title-box h3 { font-size: 1.1em; }
  .summary-assignment-text p { font-size: 1em; }

  .flowchart-popup-content { padding: 15px; }
  .flowchart-popup-content h2 { font-size: 1.2em; }
  .flowchart-step h3 { font-size: 1.05em; }
  .flowchart-step p { font-size: 0.9em; }
  
  #health-issues-section h2, #horizontal-doctor-scroll-section h2, #horizontal-nurse-scroll-section h2, .section-title {
      font-size: 1.3em; margin-bottom: 15px;
  }
  .health-issues-grid { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; padding:10px; }
  .health-issue-item { padding: 10px; }
  .health-issue-icon-placeholder { width: 35px; height: 35px; margin-bottom: 8px; }
  .health-issue-item span { font-size: 0.9em; }
} 
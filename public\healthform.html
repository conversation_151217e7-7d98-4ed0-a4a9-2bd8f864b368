<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Health Issue | DhipyCare</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 420px;
            margin: 40px auto;
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 4px 24px rgba(58,90,159,0.10);
            padding: 32px 24px 24px 24px;
        }
        .form-title {
            color: #3A5A9F;
            font-size: 1.5em;
            margin-bottom: 18px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 18px;
        }
        label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: 500;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d0d4e0;
            border-radius: 7px;
            font-size: 1em;
            background: #f9fafd;
            box-sizing: border-box;
        }
        textarea { resize: vertical; }
        button[type="submit"] {
            background: #3A5A9F;
            color: #fff;
            border: none;
            border-radius: 25px;
            padding: 12px 0;
            width: 100%;
            font-size: 1.1em;
            margin-top: 10px;
            cursor: pointer;
            transition: background 0.2s;
        }
        button[type="submit"]:hover {
            background: #2c4a8c;
        }
        .back-btn {
            display: flex;
            align-items: center;
            background: none;
            border: none;
            color: #3A5A9F;
            font-size: 1em;
            margin-bottom: 18px;
            cursor: pointer;
        }
        .back-btn i {
            margin-right: 7px;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="back-btn" onclick="window.location.href='home.html';"><i class="fas fa-arrow-left"></i>Back to Home</button>
        <div class="form-title" id="formTitle">Book a Consultation</div>
        <form id="healthForm" autocomplete="off">
            <div class="form-group">
                <label for="healthIssue">Health Issue</label>
                <input type="text" id="healthIssue" name="healthIssue" readonly>
            </div>
            <div class="form-group">
                <label for="patientName">Patient Name</label>
                <input type="text" id="patientName" name="patientName" required>
            </div>
            <div class="form-group">
                <label for="patientAge">Age</label>
                <input type="number" id="patientAge" name="patientAge" min="0" max="120" required>
            </div>
            <div class="form-group">
                <label for="patientGender">Gender</label>
                <select id="patientGender" name="patientGender" required>
                    <option value="">Select</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                </select>
            </div>
            <div class="form-group">
                <label for="patientPhone">Phone Number</label>
                <input type="tel" id="patientPhone" name="patientPhone" pattern="[0-9]{10}" required>
            </div>
            <div class="form-group">
                <label for="patientAddress">Address</label>
                <textarea id="patientAddress" name="patientAddress" rows="2" required></textarea>
            </div>
            <div class="form-group">
                <label for="paymentMethod">Payment Method</label>
                <select id="paymentMethod" name="paymentMethod" required>
                    <option value="">Select</option>
                    <option value="cash">Cash</option>
                    <option value="upi">UPI</option>
                    <option value="card">Card</option>
                </select>
            </div>
            <button type="submit">Submit</button>
        </form>
    </div>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script>
    // Read health issue from URL param
    function getQueryParam(name) {
        const url = new URL(window.location.href);
        return url.searchParams.get(name) || '';
    }
    document.addEventListener('DOMContentLoaded', function() {
        const healthIssue = getQueryParam('issue');
        document.getElementById('healthIssue').value = healthIssue || '';
        if (healthIssue) {
            document.getElementById('formTitle').textContent = `Book for: ${healthIssue}`;
        }
        // Prefill address from localStorage if available
        const savedAddress = localStorage.getItem('userSelectedLocation');
        if (savedAddress) document.getElementById('patientAddress').value = savedAddress;
        // Prevent actual submit
        document.getElementById('healthForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Form submitted! (You can handle the data as needed)');
        });
    });
    </script>
</body>
</html> 
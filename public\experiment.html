<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Dynamic Banners</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            background-color: #f0f4f7; /* Slightly cooler background */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }

        .carousel-container {
            width: 90%;
            max-width: 500px; /* Adjusted for a more app-like feel */
            overflow: hidden;
            position: relative;
            border-radius: 15px; /* More rounded corners */
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .carousel-track {
            display: flex;
            transition: transform 0.5s ease-in-out;
        }

        .carousel-banner {
            min-width: 100%;
            flex-shrink: 0;
            height: 220px; /* Adjusted height - common for app banners */
            display: flex;
            color: white;
            box-sizing: border-box;
            align-items: stretch; /* Make inner content stretch */
            justify-content: stretch;
        }

        /* Common structure for all banners now: .app-banner-content */
        .app-banner-content {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            text-align: left;
            padding: 20px;
            box-sizing: border-box;
            position: relative; /* For potential absolute positioned elements like badges */
        }

        .app-banner-text-area {
            flex-basis: 58%; /* Adjust as needed */
            padding-right: 15px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .app-banner-image-area {
            flex-basis: 38%; /* Adjust as needed */
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            overflow: hidden; /* To contain image nicely */
            border-radius: 8px; /* Rounded corners for image area */
        }

        .app-banner-image-area img {
            max-width: 100%;
            max-height: 130px; /* Control image height */
            height: auto;
            border-radius: 6px;
            display: block;
            object-fit: contain; /* Or 'cover' depending on image aspect ratio */
        }

        .app-banner-text-area h2 {
            font-size: 1.4em; /* Adjusted for new banner height */
            font-weight: 700;
            margin-top: 0;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .app-banner-text-area p.tagline, .app-banner-text-area ul {
            font-size: 0.75em; /* Smaller for subtext/list */
            margin-bottom: 12px;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.9);
        }
         .app-banner-text-area ul {
            list-style: none; /* Remove default bullets */
            padding-left: 0;
        }
        .app-banner-text-area ul li {
            margin-bottom: 4px;
            display: flex;
            align-items: flex-start;
        }
        .app-banner-text-area ul li::before { /* Custom bullet using Unicode */
            content: '✓'; /* Checkmark */
            margin-right: 8px;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.9);
        }


        .app-banner-cta-button {
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.75em; /* Smaller button text */
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            margin-top: auto; /* Pushes button to bottom if text area is flex column */
            background-color: #ffffff; /* Default white button */
            color: #333333; /* Default dark text for white button */
        }
        .app-banner-cta-button:hover { opacity: 0.85; transform: translateY(-1px); }

        /* Offer Highlight Badge */
        .offer-badge {
            background-color: #FFD700; /* Gold color for offer */
            color: #333;
            font-size: 0.7em;
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: 700;
            display: inline-block;
            margin-top: 5px;
        }


        /* Specific Banner Backgrounds (Gradients) & Button Text Colors */
        #banner1 { background: linear-gradient(135deg, #005f73 0%, #007991 100%); }
        #banner1 .app-banner-cta-button { color: #005f73; }

        #banner2 { background: linear-gradient(135deg, #ff7f50 0%, #ffa07a 100%); }
        #banner2 .app-banner-cta-button { color: #ff7f50; }

        #banner3 { background: linear-gradient(135deg, #28a745 0%, #34cc54 100%); }
        #banner3 .app-banner-cta-button { color: #28a745; }

        #banner4 { background: linear-gradient(135deg, #6f42c1 0%, #8d5fd3 100%); }
        #banner4 .app-banner-cta-button { color: #6f42c1; }
        
        #banner1-clone { background: linear-gradient(135deg, #005f73 0%, #007991 100%); }


        .carousel-button { /* Prev/Next Buttons */
            position: absolute; top: 50%; transform: translateY(-50%);
            background-color: rgba(0, 0, 0, 0.3); color: white; border: none;
            padding: 8px; font-size: 18px; cursor: pointer; z-index: 10;
            border-radius: 50%; width: 30px; height: 30px; display: flex;
            justify-content: center; align-items: center;
        }
        .carousel-button.prev { left: 8px; }
        .carousel-button.next { right: 8px; }


        /* Responsive adjustments */
        @media (max-width: 480px) { /* Targeting smaller mobile screens more specifically */
            .carousel-banner { height: 200px; } /* Slightly smaller height */
            .app-banner-content { padding: 15px; flex-direction: column; text-align:center; }
            .app-banner-image-area {
                flex-basis: auto; width: 50%; max-height: 70px; margin: 0 auto 10px auto; order: -1; /* Image on top */
            }
            .app-banner-text-area {
                flex-basis: auto; width: 100%; padding-right: 0; text-align: center;
            }
            .app-banner-text-area h2 { font-size: 1.2em; margin-bottom: 5px; }
            .app-banner-text-area p.tagline, .app-banner-text-area ul { font-size: 0.7em; margin-bottom: 8px; }
            .app-banner-text-area ul li::before { margin-right: 5px; }
            .app-banner-cta-button { font-size: 0.7em; padding: 6px 12px; width: 80%; margin: 5px auto 0 auto; }
            .offer-badge { font-size: 0.65em; }
        }
    </style>
</head>
<body>

    <div class="carousel-container">
        <div class="carousel-track">
            <div class="carousel-banner" id="banner1">
                <div class="app-banner-content">
                    <div class="app-banner-text-area">
                        <h2>Home Care Service</h2>
                        <p class="tagline">Compassionate support at home.</p>
                        <ul>
                            <li>Daily living assistance</li>
                            <li>Medication management</li>
                            <li>Mobility support</li>
                        </ul>
                        <button class="app-banner-cta-button">Explore Services</button>
                    </div>
                    <div class="app-banner-image-area">
                         <img src="https://via.placeholder.com/150x100/FFFFFF/005f73?text=Caregiver" alt="Home Care">
                    </div>
                </div>
            </div>

            <div class="carousel-banner" id="banner2">
                <div class="app-banner-content">
                    <div class="app-banner-text-area">
                        <h2>Smart Doctor Visit</h2>
                        <ul>
                            <li>Instant medicine at home</li>
                            <li>7 days follow-up</li>
                            <li>Very low cost</li>
                        </ul>
                        <div class="offer-badge">₹50 OFF First Visit!</div>
                        <button class="app-banner-cta-button">Book Visit</button>
                    </div>
                    <div class="app-banner-image-area">
                        <img src="https://via.placeholder.com/150x100/FFFFFF/ff7f50?text=Doctor+Online" alt="Doctor Visit">
                    </div>
                </div>
            </div>

            <div class="carousel-banner" id="banner3">
                 <div class="app-banner-content">
                    <div class="app-banner-text-area">
                        <h2>Emergency Service</h2>
                        <p class="tagline">24x7 Quick Response</p>
                        <ul>
                            <li>Bike ambulance team</li>
                            <li>Trained professionals</li>
                            <li>Minor emergencies covered</li>
                        </ul>
                        <button class="app-banner-cta-button">Get Help Now</button>
                    </div>
                    <div class="app-banner-image-area">
                        <img src="https://via.placeholder.com/150x100/FFFFFF/28a745?text=Emergency" alt="Emergency Service">
                    </div>
                </div>
            </div>

            <div class="carousel-banner" id="banner4">
                <div class="app-banner-content">
                    <div class="app-banner-text-area">
                        <h2>Online Pharmacy</h2>
                        <p class="tagline">Medicines at your doorstep.</p>
                        <ul>
                            <li>Wide range available</li>
                            <li>Easy prescription upload</li>
                            <li>Fast & reliable delivery</li>
                        </ul>
                        <button class="app-banner-cta-button">Shop Medicines</button>
                    </div>
                    <div class="app-banner-image-area">
                        <img src="https://via.placeholder.com/150x100/FFFFFF/6f42c1?text=Pharmacy" alt="Online Pharmacy">
                    </div>
                </div>
            </div>

            <div class="carousel-banner" id="banner1-clone">
                 <div class="app-banner-content">
                     <div class="app-banner-text-area">
                        <h2>Home Care Service</h2>
                        <p class="tagline">Compassionate support at home.</p>
                        <ul>
                            <li>Daily living assistance</li>
                            <li>Medication management</li>
                            <li>Mobility support</li>
                        </ul>
                        <button class="app-banner-cta-button">Explore Services</button>
                    </div>
                    <div class="app-banner-image-area">
                         <img src="https://via.placeholder.com/150x100/FFFFFF/005f73?text=Caregiver" alt="Home Care">
                    </div>
                </div>
            </div>
        </div>
        <button class="carousel-button prev" onclick="moveBannerManually(-1)">&#10094;</button>
        <button class="carousel-button next" onclick="moveBannerManually(1)">&#10095;</button>
    </div>

    <script>
        // JavaScript code (same as previous version for carousel functionality)
        const track = document.querySelector('.carousel-track');
        const banners = Array.from(track.children);

        if (banners.length > 0) {
            let bannerWidth; 
            let currentIndex = 0;
            const actualBannersCount = banners.length - 1;

            function initializeCarousel() {
                if (!banners[0]) return; 
                bannerWidth = banners[0].getBoundingClientRect().width;
                if (bannerWidth === 0 && document.readyState !== 'complete') {
                    window.addEventListener('load', initializeCarousel);
                    return;
                }
                if (bannerWidth === 0) {
                     bannerWidth = banners[0].offsetWidth; 
                }
                updateSlidePosition(true);
            }

            function updateSlidePosition(disableTransition = false) {
                if (!bannerWidth || bannerWidth === 0) {
                    if (banners.length > 0 && banners[0]) {
                         bannerWidth = banners[0].getBoundingClientRect().width || banners[0].offsetWidth;
                    }
                    if (!bannerWidth || bannerWidth === 0) return; 
                }
                if (disableTransition) {
                    track.style.transition = 'none';
                }
                track.style.transform = 'translateX(-' + bannerWidth * currentIndex + 'px)';
                if (disableTransition) {
                    setTimeout(() => {
                        track.style.transition = 'transform 0.5s ease-in-out';
                    }, 20);
                }
            }

            function moveToNextSlide() {
                currentIndex++;
                updateSlidePosition();
                if (currentIndex >= actualBannersCount) { 
                    setTimeout(() => {
                        currentIndex = 0;
                        updateSlidePosition(true);
                    }, 500); 
                }
            }

            function moveToPrevSlide() {
                if (currentIndex === 0) {
                    currentIndex = actualBannersCount;
                    updateSlidePosition(true);
                    setTimeout(() => {
                         currentIndex = actualBannersCount - 1;
                         if(currentIndex < 0) currentIndex = 0;
                         updateSlidePosition();
                    }, 20);
                } else {
                    currentIndex--;
                    updateSlidePosition();
                }
            }

            window.moveBannerManually = function(direction) {
                if (direction === 1) { moveToNextSlide(); } 
                else if (direction === -1) { moveToPrevSlide(); }
                resetSlideshowTimer();
            };

            let slideshowInterval = setInterval(moveToNextSlide, 3000); // Autoplay interval
            function resetSlideshowTimer() {
                clearInterval(slideshowInterval);
                slideshowInterval = setInterval(moveToNextSlide, 3000);
            }
            
            if (document.readyState === "loading") {
                document.addEventListener("DOMContentLoaded", initializeCarousel);
            } else { 
                initializeCarousel();
            }
            
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    initializeCarousel(); 
                }, 250);
            });

        } else {
            console.error("Carousel track or banners not found.");
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>DhipyCare - Home</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
    <style>
        /* General Body Styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            background-color: #f4f7f6; /* Light background for the main content area */
            color: #333;
            overflow-x: hidden; /* Prevent horizontal scroll issues with sidebar */
        }

        /* Sidebar Styles */
        .sidebar {
            height: 100%;
            width: 0; /* Initially hidden */
            position: fixed;
            z-index: 1001; /* Higher than sticky header */
            top: 0;
            left: 0;
            background-color: #ffffff; /* Clean white background */
            overflow-x: hidden;
            transition: 0.3s ease-in-out; /* Smooth transition for open/close */
            box-shadow: 2px 0 10px rgba(0,0,0,0.1); /* Subtle shadow */
            display: flex;
            flex-direction: column;
        }

        .sidebar.open {
            width: 280px; /* Width when open */
        }

        .sidebar-content {
            padding: 20px;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .close-sidebar {
            position: absolute;
            top: 15px;
            right: 25px;
            font-size: 36px;
            font-weight: bold;
            color: #555;
            cursor: pointer;
            transition: color 0.2s;
        }

        .close-sidebar:hover {
            color: #1a73e8;
        }

        .sidebar-content h3 { /* "Profile" heading in sidebar */
            margin-top: 40px;
            margin-bottom: 15px;
            font-size: 1.4em;
            color: #223a7a;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        #sidebar-profile-summary {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding: 10px;
            background-color: #f0f2f5;
            border-radius: 8px;
        }

        #sidebar-profile-photo-container {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #ccc; /* Placeholder background */
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden; /* Ensure images fit */
        }

        #sidebar-profile-photo-container img,
        #sidebar-profile-photo-container svg {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
         #sidebar-profile-name {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
        }

        .sidebar-content ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            flex-grow: 1;
        }

        .sidebar-content ul li {
            padding: 12px 15px;
            text-decoration: none;
            font-size: 1em;
            color: #444;
            display: block;
            transition: background-color 0.2s, color 0.2s;
            cursor: pointer;
            border-radius: 6px;
            margin-bottom: 5px;
        }

        .sidebar-content ul li:hover {
            background-color: #e9f5ff;
            color: #1a73e8;
        }

        .sidebar-content ul li#my-account-btn:before { content: "👤 "; margin-right: 8px; }
        .sidebar-content ul li#my-bookings-btn:before { content: "📅 "; margin-right: 8px; }
        .sidebar-content ul li#terms-conditions-btn:before { content: "📜 "; margin-right: 8px; }
        .sidebar-content ul li#contact-us-btn:before { content: "📞 "; margin-right: 8px; }
        .sidebar-content ul li#logout-btn:before { content: "🚪 "; margin-right: 8px; }


        .sidebar-content ul li#logout-btn {
            margin-top: auto;
            border-top: 1px solid #eee;
            padding-top: 15px;
            color: #d9534f;
        }

        .sidebar-content ul li#logout-btn:hover {
            background-color: #fbeae9;
            color: #c9302c;
        }

        #sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
            z-index: 1000;
            transition: opacity 0.3s ease-in-out;
            opacity: 0;
        }

        #sidebar-overlay.active {
            display: block;
            opacity: 1;
        }

        /* Profile Modal Styles */
        .profile-modal {
            display: none; /* Hidden by default */
            position: fixed;
            z-index: 2000; /* Above sidebar overlay */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto; /* Enable scroll if needed */
            background-color: rgba(0,0,0,0.5); /* Dim background */
            align-items: center; /* Vertical center */
            justify-content: center; /* Horizontal center */
        }

        .profile-modal-content {
            background-color: #fff;
            margin: auto; /* Responsive margin */
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            width: 90%;
            max-width: 500px; /* Max width */
            position: relative; /* For close button positioning */
            animation: fadeInModal 0.3s ease-out;
        }

        @keyframes fadeInModal {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .close-profile-modal {
            color: #aaa;
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-profile-modal:hover,
        .close-profile-modal:focus {
            color: #333;
            text-decoration: none;
        }

        .profile-modal-content h2 {
            text-align: center;
            color: #223a7a;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.6em;
        }

        #modal-profile-photo-container {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #e0e8f0;
            margin: 0 auto 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border: 3px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        #modal-profile-photo-container img,
        #modal-profile-photo-container svg {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-modal-details p {
            font-size: 1em;
            color: #444;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
        }
        .profile-modal-details p:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .profile-modal-details p strong {
            color: #223a7a;
            margin-right: 10px;
        }
        .profile-modal-details span {
            text-align: right;
            color: #555;
        }


        /* Header Styles */
        .modern-header {
            background: linear-gradient(90deg, #DC2626 0%, #EF4444 100%);
            color: white;
            padding: 10px 15px;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .modern-header-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        .header-profile-section {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        .profile-icon {
            /* Using an SVG for the profile icon */
            margin-right: 8px;
            width: 28px; /* Adjust size as needed */
            height: 28px; /* Adjust size as needed */
        }
        #header-profile-name {
            font-weight: 500;
            font-size: 0.95em;
        }
        .modern-location {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background-color 0.2s;
            background-color: rgba(255,255,255,0.1);
        }
        .modern-location:hover {
            background-color: rgba(255,255,255,0.2);
        }
        .location-icon {
            margin-right: 6px;
            font-size: 1.1em; /* Using text icon: 📍 */
        }
        .modern-location-text {
            font-size: 0.9em;
            font-weight: 500;
            margin-right: 4px;
        }
        .chevron {
            font-size: 0.7em; /* Using text icon: ▼ */
        }

        /* Location Modal Styles */
        .location-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.65); /* Darker overlay */
            display: none; /* Hidden by default */
            align-items: center; justify-content: center;
            z-index: 2000;
            opacity: 0; transition: opacity 0.3s ease-in-out;
        }
        .location-overlay.active {
            display: flex;
            opacity: 1;
        }
        .location-modal {
            background-color: white; padding: 30px; border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            width: 90%; max-width: 550px;
            transform: scale(0.9); transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Pop effect */
            position: relative;
        }
        .location-overlay.active .location-modal { transform: scale(1); }

        .close-location-modal {
            position: absolute; top: 12px; right: 18px; font-size: 32px; /* Larger */
            font-weight: bold; color: #888; cursor: pointer; transition: color 0.2s, transform 0.2s;
            line-height: 1; /* Ensure proper alignment */
        }
        .close-location-modal:hover { color: #333; transform: rotate(90deg); }

        .location-modal h2 {
            color: #223a7a; font-size: 1.6em; margin-top: 0; margin-bottom: 25px; text-align: center;
            font-weight: 600;
        }
        .location-modal #map {
            height: 200px; /* Fixed height for the map */
            margin-bottom: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        #selected-address {
            font-weight: 500;
            font-size: 0.95em;
            color: #333;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 6px;
            border: 1px solid #eee;
            min-height: 1.5em; /* Ensure it has some height even when empty */
            text-align: center;
        }
        .location-modal input[type="text"] {
            width: 100%; padding: 14px; margin-bottom: 18px;
            border: 1px solid #ccc; border-radius: 8px; box-sizing: border-box; font-size: 1em;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .location-modal input[type="text"]:focus {
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.15);
            outline: none;
        }
        .location-modal button {
            width: 100%; padding: 14px; border-radius: 8px; border: none;
            font-size: 1.05em; font-weight: bold; cursor: pointer; transition: background-color 0.2s, transform 0.1s;
        }
        .location-modal button:active {
            transform: scale(0.98);
        }
        #use-current-location {
            background-color: #e9f5ff; color: #1976D2; margin-bottom: 12px;
            border: 1px solid #1976D2;
        }
        #use-current-location:hover { background-color: #d4e9ff; }
        #location-submit {
            background: linear-gradient(90deg, #1976D2, #3ec6e0); color: white;
        }
        #location-submit:disabled { background: #ccc; cursor: not-allowed; }
        #location-submit:hover:not(:disabled) { opacity:0.9; }
        .location-error {
            color: #e74c3c; font-size: 0.9em; text-align: center; margin-top: 12px; min-height: 1.2em;
            font-weight: 500;
        }

        /* Main content padding */
        .main-content-area {
            padding: 15px;
            padding-top: 75px; /* Adjust if header height changes */
        }

        .section-title {
          text-align: center; color: #223a7a; margin-top:10px; margin-bottom: 20px; font-size: 1.5em; font-weight: 600;
        }

        /* Service/Horizontal Cards */
        .modern-horizontal-cards {
            display: flex;
            justify-content: space-around;
            padding: 15px 0; /* Reduced side padding as main-content-area has it */
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .modern-h-card {
            flex-basis: calc(33.333% - 20px);
            min-width: 280px;
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: flex-start; /* Align items to the top */
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            color: #333;
            border: 1px solid #e0e8f0;
        }

        .modern-h-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .modern-h-card-img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-right: 18px;
            object-fit: contain;
            flex-shrink: 0; /* Prevent image from shrinking */
        }

        .modern-h-card-content { /* New wrapper for title and features */
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .modern-h-card-title { /* Replaces modern-h-card-label */
            font-size: 1.1em;
            font-weight: bold;
            color: #223a7a;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .modern-h-card-features {
            list-style-type: disc;
            margin: 0;
            padding-left: 20px;
            font-size: 0.9em;
            color: #333;
            line-height: 1.5;
        }

        .modern-h-card-features li {
            margin-bottom: 4px;
        }

        /* .modern-h-card-label and .modern-h-card-arrow are no longer used directly, replaced by title and features */

        /* Health Issues Grid */
        .health-issues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            padding: 15px 0; /* Reduced side padding */
            margin-bottom: 20px;
        }
        .health-issue-item {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: box-shadow 0.2s, transform 0.2s, border-color 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .health-issue-item:hover {
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            border-color: #1976D2;
        }
        .health-issue-icon-placeholder {
            width: 40px;
            height: 40px;
            background-color: #e9f5ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            color: #1976D2;
            font-weight: bold;
            font-size: 0.9em;
        }
        .health-issue-item span {
            font-size: 0.95em;
            color: #333;
            font-weight: 500;
        }

        /* Scrolling Wrappers */
        .scrolling-wrapper-container {
          overflow: hidden;
          padding: 10px 0;
          margin-bottom: 15px;
          position: relative;
        }
        .scrolling-wrapper {
          display: flex;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          padding-bottom: 15px; /* space for scrollbar */
          gap: 15px;
          /* width: max-content; Remove this to allow scrollbar to appear correctly */
        }

        .scrolling-wrapper::-webkit-scrollbar {
          height: 8px;
        }
        .scrolling-wrapper::-webkit-scrollbar-track {
          background: #f0f2f5;
          border-radius: 4px;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb {
          background: #1976D2;
          border-radius: 4px;
          transition: background-color 0.2s;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb:hover {
          background: #145a9e;
        }

        .scrolling-wrapper { /* For Firefox */
          scrollbar-width: thin;
          scrollbar-color: #1976D2 #f0f2f5;
        }
        .doctor-scroll-item, .nurse-scroll-item, .assistant-scroll-item {
            flex: 0 0 auto;
            width: 150px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.07);
            padding: 12px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .doctor-scroll-item:hover, .nurse-scroll-item:hover, .assistant-scroll-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(0,0,0,0.1);
        }
        .doctor-scroll-item img, .nurse-scroll-item img, .assistant-scroll-item img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 8px;
            border: 2px solid #e0e8f0;
        }
        .doctor-name, .nurse-name, .assistant-name {
            display: block;
            font-weight: 600;
            color: #223a7a;
            font-size: 1em;
            margin-bottom: 4px;
        }
        .doctor-description, .nurse-description, .assistant-info {
            font-size: 0.85em;
            color: #555;
            line-height: 1.3;
        }

        /* FAQ Section */
        .faq-list { max-width: 700px; margin: 0 auto 20px auto; }
        .faq-item {
            background-color: #fff; border: 1px solid #e0e0e0;
            border-radius: 6px; margin-bottom: 10px; overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .faq-question {
            width: 100%; background-color: transparent; border: none;
            padding: 15px; text-align: left; font-size: 1.05em; font-weight: 500;
            color: #333; cursor: pointer; display: flex; justify-content: space-between;
            align-items: center;
        }
        .faq-question::after {
            content: '\\276F'; /* Chevron right */
            font-size: 1.2em; color: #1976D2;
            transition: transform 0.3s ease;
        }
        .faq-item.active .faq-question::after { transform: rotate(90deg); }
        .faq-answer {
            padding: 0 15px; max-height: 0; overflow: hidden;
            font-size: 0.95em; color: #555; line-height: 1.6;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }
        .faq-item.active .faq-answer {
            max-height: 300px; /* Increased max-height */
            padding-bottom: 15px;
        }

        /* Provider List Section */
        #provider-list-section {
            padding: 15px;
            background-color: #fff;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        #back-to-services-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-services-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }
        #provider-list-title {
            text-align: center;
            color: #223a7a;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        #provider-list-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        .provider-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #eee;
            box-shadow: 0 3px 7px rgba(0,0,0,0.07);
            display: flex;
            flex-direction: column;
        }
        .provider-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .provider-card-img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid #1976D2;
        }
        .provider-card-info h4 {
            margin: 0 0 4px 0;
            color: #223a7a;
            font-size: 1.1em;
        }
        .provider-card-info p {
            margin: 0;
            font-size: 0.9em;
            color: #555;
        }
        .provider-card-details {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .provider-select-btn {
            margin-top: auto; /* Pushes button to the bottom */
            padding: 10px 15px;
            border-radius: 6px;
            background-color: #1976D2;
            color: white;
            text-align: center;
            font-weight: bold;
            cursor: pointer;
            border: none;
            transition: background-color 0.2s;
        }
        .provider-select-btn:hover {
            background-color: #145a9e;
        }

        /* Patient Details Form for Health Issue */
        #health-issue-patient-form-section .form-group {
            margin-bottom: 15px;
        }
        #health-issue-patient-form-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        #health-issue-patient-form-section input[type=\"text\"],
        #health-issue-patient-form-section input[type=\"number\"],
        #health-issue-patient-form-section input[type=\"tel\"],
        #health-issue-patient-form-section select,
        #health-issue-patient-form-section textarea {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ccc;
            box-sizing: border-box;
            font-size: 1em;
        }
        #health-issue-patient-form-section textarea {
            min-height: 80px;
        }
        #health-issue-patient-form-section button[type=\"submit\"] {
            width: 100%;
            padding: 14px;
            border-radius: 8px;
            border: none;
            background: linear-gradient(90deg, #1976D2, #3ec6e0);
            color: white;
            font-size: 1.05em;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        #health-issue-patient-form-section button[type=\"submit\"]:hover {
            opacity: 0.9;
        }
        #back-to-overview-from-patient-form-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-overview-from-patient-form-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }

        /* Responsive Styles */
        @media screen and (max-width: 768px) {
            .sidebar.open {
                width: 250px;
            }
            .sidebar-content h3 {
                font-size: 1.3em;
            }
            #sidebar-profile-name {
                font-size: 1em;
            }
            .sidebar-content ul li {
                font-size: 0.95em;
                padding: 10px 12px;
            }

            .main-content-area {
                padding: 10px;
                padding-top: 65px; /* Adjust if header height changes */
            }
            .modern-header { padding: 8px 10px; }
            .header-profile-section { flex-shrink: 0; }
            #header-profile-name {
                font-size: 0.9em;
                max-width: 100px; /* Ensure name doesn't push content */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .modern-location { padding: 6px 8px; }
            .modern-location-text { font-size: 0.85em; }
            .location-icon { font-size: 1em; }
            .chevron { font-size: 0.6em; }

            .modern-horizontal-cards { flex-direction: column; padding: 10px 0; gap:10px; }
            .modern-h-card { min-width: unset; width:100%; flex-basis: auto; }
            .modern-h-card-title { font-size: 1em; }

            .section-title { font-size: 1.3em; margin-bottom: 15px; }
            .health-issues-grid { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; padding:10px 0; }
            .health-issue-item { padding: 10px; }
            .health-issue-icon-placeholder { width: 35px; height: 35px; margin-bottom: 8px; }
            .health-issue-item span { font-size: 0.9em; }

            .profile-modal-content { width: 95%; padding: 20px; }
            .profile-modal-content h2 { font-size: 1.4em; }
            #modal-profile-photo-container { width: 100px; height: 100px; }
            .profile-modal-details p { font-size: 0.95em; }

            .location-modal { width: 95%; padding: 20px; }
            .location-modal h2 { font-size: 1.4em; }
            .location-modal #map { height: 180px; }
        }

        /* Fallback for profile icon if SVG fails or for general use */
        .default-profile-icon-fallback {
            display: inline-block;
            width: 24px;
            height: 24px;
            background-color: #fff; /* White background */
            border-radius: 50%;
            text-align: center;
            line-height: 24px; /* Vertically center */
            font-weight: bold;
            color: #1976D2; /* Blue initial */
            font-size: 14px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .main-content-area {
                padding: 15px;
            }

            .modern-horizontal-cards, /* Keep for provider list if it uses this class */
            .modern-vertical-service-cards {
                grid-template-columns: 1fr; /* Fallback for vertical stack */
                display: flex; /* For vertical stack */
                flex-direction: column; /* For vertical stack */
            }

            .health-issues-grid {
                grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
                gap: 15px;
            }

            .modern-h-card,
            .health-issue-item,
            .patient-form {
                padding: 20px;
            }

            .modern-h-card-img {
                width: 50px;
                height: 50px;
            }

            .modern-h-card-title {
                font-size: 18px;
            }

            /* Quick Actions Responsive */
            #quick-actions-container {
                flex-direction: column;
                gap: 10px;
            }
            .quick-action-card {
                padding: 15px;
            }
            .quick-action-icon {
                width: 35px; height: 35px; margin-bottom: 8px;
            }
            .quick-action-text {
                font-size: 1em;
            }
        }

        /* NEW STYLES FOR QUICK ACTIONS AND REVISED LAYOUT */
        #quick-actions-container {
            display: flex;
            flex-direction: column; /* Stack quick actions vertically */
            gap: 10px; /* Space between quick action cards */
            margin-bottom: 30px;
        }

        .quick-action-card {
            background-color: #fff;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: box-shadow 0.2s ease;
            border: 1px solid #e8e8e8;
        }

        .quick-action-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }

        .quick-action-icon {
            width: 40px; /* Adjust as needed */
            height: 40px; /* Adjust as needed */
            margin-right: 15px;
            object-fit: contain;
        }

        .quick-action-text {
            flex-grow: 1;
            font-size: 1.1em;
            font-weight: 500;
            color: #333;
        }

        .quick-action-arrow {
            font-size: 1.2em;
            color: #888;
        }

        /* Styling for Detailed Service Cards (Vertical Stack) */
        .modern-vertical-service-cards {
            display: flex;
            flex-direction: column;
            gap: 15px; /* Space between detailed service cards */
            margin-bottom: 30px;
        }

        /* .modern-h-card styles (used for detailed cards) should already be mostly fine,
           but ensure they work well in a vertical stack.
           The existing .modern-h-card styles will be used. */

        .section-title {
            /* Ensure section titles are styled as per screenshot (prominent, blue-ish) */
            color: #1e3a8a; /* Example blue color */
            font-weight: 600;
            font-size: 1.6em; /* Make it larger */
            margin-top: 30px;
            margin-bottom: 20px;
            text-align: left; /* As per screenshot */
        }

        /* FAQ Styling Update */
        .faq-item .faq-question::after {
            content: '+'; /* Change chevron to plus */
            font-size: 1.5em; /* Make plus icon larger */
            color: #1e3a8a; /* Match title color */
        }

        .faq-item.active .faq-question::after {
            content: '−'; /* Change to minus when active */
            transform: none; /* Remove rotation */
        }

        /* NEW HEADER STYLES */
        .new-main-header {
            background-color: #DC2626; /* Dark red background */
            color: white;
            padding: 10px 15px;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .user-info-line {
            display: flex;
            align-items: center;
            margin-bottom: 8px; /* Space between user line and location line */
        }
        .user-avatar-icon {
            width: 32px; /* Adjust size */
            height: 32px; /* Adjust size */
            margin-right: 10px;
            cursor: pointer;
            border-radius: 50%; /* If the SVG itself isn't round */
        }
        .user-name-display {
            font-weight: 500;
            font-size: 1.1em;
        }
        .location-line {
            display: flex;
            align-items: center;
            cursor: pointer;
            background-color: white; /* White background for location bar */
            color: #333; /* Dark text for location */
            padding: 8px 12px;
            border-radius: 8px;
        }
        .location-pin-icon {
            margin-right: 8px;
            font-size: 1.1em;
        }
        .location-address-text {
            font-size: 0.95em;
            font-weight: 500;
            margin-right: auto; /* Pushes chevron to the right */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px; /* Adjust as needed */
        }
        .location-chevron-icon {
            font-size: 0.8em;
        }
        /* END NEW HEADER STYLES */

        /* Location Modal Styles (ensure they work with new header trigger) */
        .location-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.65); /* Darker overlay */
            display: none; /* Hidden by default */
            align-items: center; justify-content: center;
            z-index: 2000;
            opacity: 0; transition: opacity 0.3s ease-in-out;
        }
        .location-overlay.active {
            display: flex;
            opacity: 1;
        }
        .location-modal {
            background-color: white; padding: 30px; border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            width: 90%; max-width: 550px;
            transform: scale(0.9); transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Pop effect */
            position: relative;
        }
        .location-overlay.active .location-modal { transform: scale(1); }

        .close-location-modal {
            position: absolute; top: 12px; right: 18px; font-size: 32px; /* Larger */
            font-weight: bold; color: #888; cursor: pointer; transition: color 0.2s, transform 0.2s;
            line-height: 1; /* Ensure proper alignment */
        }
        .close-location-modal:hover { color: #333; transform: rotate(90deg); }

        .location-modal h2 {
            color: #223a7a; font-size: 1.6em; margin-top: 0; margin-bottom: 25px; text-align: center;
            font-weight: 600;
        }
        .location-modal #map {
            height: 200px; /* Fixed height for the map */
            margin-bottom: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        #selected-address {
            font-weight: 500;
            font-size: 0.95em;
            color: #333;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 6px;
            border: 1px solid #eee;
            min-height: 1.5em; /* Ensure it has some height even when empty */
            text-align: center;
        }
        .location-modal input[type="text"] {
            width: 100%; padding: 14px; margin-bottom: 18px;
            border: 1px solid #ccc; border-radius: 8px; box-sizing: border-box; font-size: 1em;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .location-modal input[type="text"]:focus {
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.15);
            outline: none;
        }
        .location-modal button {
            width: 100%; padding: 14px; border-radius: 8px; border: none;
            font-size: 1.05em; font-weight: bold; cursor: pointer; transition: background-color 0.2s, transform 0.1s;
        }
        .location-modal button:active {
            transform: scale(0.98);
        }
        #use-current-location {
            background-color: #e9f5ff; color: #1976D2; margin-bottom: 12px;
            border: 1px solid #1976D2;
        }
        #use-current-location:hover { background-color: #d4e9ff; }
        #location-submit {
            background: linear-gradient(90deg, #1976D2, #3ec6e0); color: white;
        }
        #location-submit:disabled { background: #ccc; cursor: not-allowed; }
        #location-submit:hover:not(:disabled) { opacity:0.9; }
        .location-error {
            color: #e74c3c; font-size: 0.9em; text-align: center; margin-top: 12px; min-height: 1.2em;
            font-weight: 500;
        }

        /* Main content padding */
        .main-content-area {
            padding: 15px;
            padding-top: 75px; /* Adjust if header height changes */
        }

        .section-title {
          text-align: center; color: #223a7a; margin-top:10px; margin-bottom: 20px; font-size: 1.5em; font-weight: 600;
        }

        /* Service/Horizontal Cards */
        .modern-horizontal-cards {
            display: flex;
            justify-content: space-around;
            padding: 15px 0; /* Reduced side padding as main-content-area has it */
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .modern-h-card {
            flex-basis: calc(33.333% - 20px);
            min-width: 280px;
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: flex-start; /* Align items to the top */
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            color: #333;
            border: 1px solid #e0e8f0;
        }

        .modern-h-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .modern-h-card-img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-right: 18px;
            object-fit: contain;
            flex-shrink: 0; /* Prevent image from shrinking */
        }

        .modern-h-card-content { /* New wrapper for title and features */
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .modern-h-card-title { /* Replaces modern-h-card-label */
            font-size: 1.1em;
            font-weight: bold;
            color: #223a7a;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .modern-h-card-features {
            list-style-type: disc;
            margin: 0;
            padding-left: 20px;
            font-size: 0.9em;
            color: #333;
            line-height: 1.5;
        }

        .modern-h-card-features li {
            margin-bottom: 4px;
        }

        /* .modern-h-card-label and .modern-h-card-arrow are no longer used directly, replaced by title and features */

        /* Health Issues Grid */
        .health-issues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            padding: 15px 0; /* Reduced side padding */
            margin-bottom: 20px;
        }
        .health-issue-item {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: box-shadow 0.2s, transform 0.2s, border-color 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .health-issue-item:hover {
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            border-color: #1976D2;
        }
        .health-issue-icon-placeholder {
            width: 40px;
            height: 40px;
            background-color: #e9f5ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            color: #1976D2;
            font-weight: bold;
            font-size: 0.9em;
        }
        .health-issue-item span {
            font-size: 0.95em;
            color: #333;
            font-weight: 500;
        }

        /* Scrolling Wrappers */
        .scrolling-wrapper-container {
          overflow: hidden;
          padding: 10px 0;
          margin-bottom: 15px;
          position: relative;
        }
        .scrolling-wrapper {
          display: flex;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          padding-bottom: 15px; /* space for scrollbar */
          gap: 15px;
          /* width: max-content; Remove this to allow scrollbar to appear correctly */
        }

        .scrolling-wrapper::-webkit-scrollbar {
          height: 8px;
        }
        .scrolling-wrapper::-webkit-scrollbar-track {
          background: #f0f2f5;
          border-radius: 4px;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb {
          background: #1976D2;
          border-radius: 4px;
          transition: background-color 0.2s;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb:hover {
          background: #145a9e;
        }

        .scrolling-wrapper { /* For Firefox */
          scrollbar-width: thin;
          scrollbar-color: #1976D2 #f0f2f5;
        }
        .doctor-scroll-item, .nurse-scroll-item, .assistant-scroll-item {
            flex: 0 0 auto;
            width: 150px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.07);
            padding: 12px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .doctor-scroll-item:hover, .nurse-scroll-item:hover, .assistant-scroll-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(0,0,0,0.1);
        }
        .doctor-scroll-item img, .nurse-scroll-item img, .assistant-scroll-item img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 8px;
            border: 2px solid #e0e8f0;
        }
        .doctor-name, .nurse-name, .assistant-name {
            display: block;
            font-weight: 600;
            color: #223a7a;
            font-size: 1em;
            margin-bottom: 4px;
        }
        .doctor-description, .nurse-description, .assistant-info {
            font-size: 0.85em;
            color: #555;
            line-height: 1.3;
        }

        /* FAQ Section */
        .faq-list { max-width: 700px; margin: 0 auto 20px auto; }
        .faq-item {
            background-color: #fff; border: 1px solid #e0e0e0;
            border-radius: 6px; margin-bottom: 10px; overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .faq-question {
            width: 100%; background-color: transparent; border: none;
            padding: 15px; text-align: left; font-size: 1.05em; font-weight: 500;
            color: #333; cursor: pointer; display: flex; justify-content: space-between;
            align-items: center;
        }
        .faq-question::after {
            content: '\\276F'; /* Chevron right */
            font-size: 1.2em; color: #1976D2;
            transition: transform 0.3s ease;
        }
        .faq-item.active .faq-question::after { transform: rotate(90deg); }
        .faq-answer {
            padding: 0 15px; max-height: 0; overflow: hidden;
            font-size: 0.95em; color: #555; line-height: 1.6;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }
        .faq-item.active .faq-answer {
            max-height: 300px; /* Increased max-height */
            padding-bottom: 15px;
        }

        /* Provider List Section */
        #provider-list-section {
            padding: 15px;
            background-color: #fff;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        #back-to-services-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-services-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }
        #provider-list-title {
            text-align: center;
            color: #223a7a;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        #provider-list-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        .provider-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #eee;
            box-shadow: 0 3px 7px rgba(0,0,0,0.07);
            display: flex;
            flex-direction: column;
        }
        .provider-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .provider-card-img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid #1976D2;
        }
        .provider-card-info h4 {
            margin: 0 0 4px 0;
            color: #223a7a;
            font-size: 1.1em;
        }
        .provider-card-info p {
            margin: 0;
            font-size: 0.9em;
            color: #555;
        }
        .provider-card-details {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .provider-select-btn {
            margin-top: auto; /* Pushes button to the bottom */
            padding: 10px 15px;
            border-radius: 6px;
            background-color: #1976D2;
            color: white;
            text-align: center;
            font-weight: bold;
            cursor: pointer;
            border: none;
            transition: background-color 0.2s;
        }
        .provider-select-btn:hover {
            background-color: #145a9e;
        }

        /* Patient Details Form for Health Issue */
        #health-issue-patient-form-section .form-group {
            margin-bottom: 15px;
        }
        #health-issue-patient-form-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        #health-issue-patient-form-section input[type=\"text\"],
        #health-issue-patient-form-section input[type=\"number\"],
        #health-issue-patient-form-section input[type=\"tel\"],
        #health-issue-patient-form-section select,
        #health-issue-patient-form-section textarea {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ccc;
            box-sizing: border-box;
            font-size: 1em;
        }
        #health-issue-patient-form-section textarea {
            min-height: 80px;
        }
        #health-issue-patient-form-section button[type=\"submit\"] {
            width: 100%;
            padding: 14px;
            border-radius: 8px;
            border: none;
            background: linear-gradient(90deg, #1976D2, #3ec6e0);
            color: white;
            font-size: 1.05em;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        #health-issue-patient-form-section button[type=\"submit\"]:hover {
            opacity: 0.9;
        }
        #back-to-overview-from-patient-form-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-overview-from-patient-form-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }

        /* Responsive Styles */
        @media screen and (max-width: 768px) {
            .sidebar.open {
                width: 250px;
            }
            .sidebar-content h3 {
                font-size: 1.3em;
            }
            #sidebar-profile-name {
                font-size: 1em;
            }
            .sidebar-content ul li {
                font-size: 0.95em;
                padding: 10px 12px;
            }

            .main-content-area {
                padding: 10px;
                padding-top: 65px; /* Adjust if header height changes */
            }
            .modern-header { padding: 8px 10px; }
            .header-profile-section { flex-shrink: 0; }
            #header-profile-name {
                font-size: 0.9em;
                max-width: 100px; /* Ensure name doesn't push content */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .modern-location { padding: 6px 8px; }
            .modern-location-text { font-size: 0.85em; }
            .location-icon { font-size: 1em; }
            .chevron { font-size: 0.6em; }

            .modern-horizontal-cards { flex-direction: column; padding: 10px 0; gap:10px; }
            .modern-h-card { min-width: unset; width:100%; flex-basis: auto; }
            .modern-h-card-title { font-size: 1em; }

            .section-title { font-size: 1.3em; margin-bottom: 15px; }
            .health-issues-grid { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; padding:10px 0; }
            .health-issue-item { padding: 10px; }
            .health-issue-icon-placeholder { width: 35px; height: 35px; margin-bottom: 8px; }
            .health-issue-item span { font-size: 0.9em; }

            .profile-modal-content { width: 95%; padding: 20px; }
            .profile-modal-content h2 { font-size: 1.4em; }
            #modal-profile-photo-container { width: 100px; height: 100px; }
            .profile-modal-details p { font-size: 0.95em; }

            .location-modal { width: 95%; padding: 20px; }
            .location-modal h2 { font-size: 1.4em; }
            .location-modal #map { height: 180px; }
        }

        /* Fallback for profile icon if SVG fails or for general use */
        .default-profile-icon-fallback {
            display: inline-block;
            width: 24px;
            height: 24px;
            background-color: #fff; /* White background */
            border-radius: 50%;
            text-align: center;
            line-height: 24px; /* Vertically center */
            font-weight: bold;
            color: #1976D2; /* Blue initial */
            font-size: 14px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .main-content-area {
                padding: 15px;
            }

            .modern-horizontal-cards, /* Keep for provider list if it uses this class */
            .modern-vertical-service-cards {
                grid-template-columns: 1fr; /* Fallback for vertical stack */
                display: flex; /* For vertical stack */
                flex-direction: column; /* For vertical stack */
            }

            .health-issues-grid {
                grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
                gap: 15px;
            }

            .modern-h-card,
            .health-issue-item,
            .patient-form {
                padding: 20px;
            }

            .modern-h-card-img {
                width: 50px;
                height: 50px;
            }

            .modern-h-card-title {
                font-size: 18px;
            }

            /* Quick Actions Responsive */
            #quick-actions-container {
                flex-direction: column;
                gap: 10px;
            }
            .quick-action-card {
                padding: 15px;
            }
            .quick-action-icon {
                width: 35px; height: 35px; margin-bottom: 8px;
            }
            .quick-action-text {
                font-size: 1em;
            }
        }

        /* NEW STYLES FOR QUICK ACTIONS AND REVISED LAYOUT */
        #quick-actions-container {
            display: flex;
            flex-direction: column; /* Stack quick actions vertically */
            gap: 10px; /* Space between quick action cards */
            margin-bottom: 30px;
        }

        .quick-action-card {
            background-color: #fff;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: box-shadow 0.2s ease;
            border: 1px solid #e8e8e8;
        }

        .quick-action-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }

        .quick-action-icon {
            width: 40px; /* Adjust as needed */
            height: 40px; /* Adjust as needed */
            margin-right: 15px;
            object-fit: contain;
        }

        .quick-action-text {
            flex-grow: 1;
            font-size: 1.1em;
            font-weight: 500;
            color: #333;
        }

        .quick-action-arrow {
            font-size: 1.2em;
            color: #888;
        }

        /* Styling for Detailed Service Cards (Vertical Stack) */
        .modern-vertical-service-cards {
            display: flex;
            flex-direction: column;
            gap: 15px; /* Space between detailed service cards */
            margin-bottom: 30px;
        }

        /* .modern-h-card styles (used for detailed cards) should already be mostly fine,
           but ensure they work well in a vertical stack.
           The existing .modern-h-card styles will be used. */

        .section-title {
            /* Ensure section titles are styled as per screenshot (prominent, blue-ish) */
            color: #1e3a8a; /* Example blue color */
            font-weight: 600;
            font-size: 1.6em; /* Make it larger */
            margin-top: 30px;
            margin-bottom: 20px;
            text-align: left; /* As per screenshot */
        }

        /* FAQ Styling Update */
        .faq-item .faq-question::after {
            content: '+'; /* Change chevron to plus */
            font-size: 1.5em; /* Make plus icon larger */
            color: #1e3a8a; /* Match title color */
        }

        .faq-item.active .faq-question::after {
            content: '−'; /* Change to minus when active */
            transform: none; /* Remove rotation */
        }

        /* NEW HEADER STYLES */
        .new-main-header {
            background-color: #1e3a8a; /* Dark blue background */
            color: white;
            padding: 10px 15px;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .user-info-line {
            display: flex;
            align-items: center;
            margin-bottom: 8px; /* Space between user line and location line */
        }
        .user-avatar-icon {
            width: 32px; /* Adjust size */
            height: 32px; /* Adjust size */
            margin-right: 10px;
            cursor: pointer;
            border-radius: 50%; /* If the SVG itself isn't round */
        }
        .user-name-display {
            font-weight: 500;
            font-size: 1.1em;
        }
        .location-line {
            display: flex;
            align-items: center;
            cursor: pointer;
            background-color: white; /* White background for location bar */
            color: #333; /* Dark text for location */
            padding: 8px 12px;
            border-radius: 8px;
        }
        .location-pin-icon {
            margin-right: 8px;
            font-size: 1.1em;
        }
        .location-address-text {
            font-size: 0.95em;
            font-weight: 500;
            margin-right: auto; /* Pushes chevron to the right */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px; /* Adjust as needed */
        }
        .location-chevron-icon {
            font-size: 0.8em;
        }
        /* END NEW HEADER STYLES */

        /* Location Modal Styles (ensure they work with new header trigger) */
        .location-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.65); /* Darker overlay */
            display: none; /* Hidden by default */
            align-items: center; justify-content: center;
            z-index: 2000;
            opacity: 0; transition: opacity 0.3s ease-in-out;
        }
        .location-overlay.active {
            display: flex;
            opacity: 1;
        }
        .location-modal {
            background-color: white; padding: 30px; border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            width: 90%; max-width: 550px;
            transform: scale(0.9); transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Pop effect */
            position: relative;
        }
        .location-overlay.active .location-modal { transform: scale(1); }

        .close-location-modal {
            position: absolute; top: 12px; right: 18px; font-size: 32px; /* Larger */
            font-weight: bold; color: #888; cursor: pointer; transition: color 0.2s, transform 0.2s;
            line-height: 1; /* Ensure proper alignment */
        }
        .close-location-modal:hover { color: #333; transform: rotate(90deg); }

        .location-modal h2 {
            color: #223a7a; font-size: 1.6em; margin-top: 0; margin-bottom: 25px; text-align: center;
            font-weight: 600;
        }
        .location-modal #map {
            height: 200px; /* Fixed height for the map */
            margin-bottom: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        #selected-address {
            font-weight: 500;
            font-size: 0.95em;
            color: #333;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 6px;
            border: 1px solid #eee;
            min-height: 1.5em; /* Ensure it has some height even when empty */
            text-align: center;
        }
        .location-modal input[type="text"] {
            width: 100%; padding: 14px; margin-bottom: 18px;
            border: 1px solid #ccc; border-radius: 8px; box-sizing: border-box; font-size: 1em;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .location-modal input[type="text"]:focus {
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.15);
            outline: none;
        }
        .location-modal button {
            width: 100%; padding: 14px; border-radius: 8px; border: none;
            font-size: 1.05em; font-weight: bold; cursor: pointer; transition: background-color 0.2s, transform 0.1s;
        }
        .location-modal button:active {
            transform: scale(0.98);
        }
        #use-current-location {
            background-color: #e9f5ff; color: #1976D2; margin-bottom: 12px;
            border: 1px solid #1976D2;
        }
        #use-current-location:hover { background-color: #d4e9ff; }
        #location-submit {
            background: linear-gradient(90deg, #1976D2, #3ec6e0); color: white;
        }
        #location-submit:disabled { background: #ccc; cursor: not-allowed; }
        #location-submit:hover:not(:disabled) { opacity:0.9; }
        .location-error {
            color: #e74c3c; font-size: 0.9em; text-align: center; margin-top: 12px; min-height: 1.2em;
            font-weight: 500;
        }

        /* Main content padding */
        .main-content-area {
            padding: 15px;
            padding-top: 75px; /* Adjust if header height changes */
        }

        .section-title {
          text-align: center; color: #223a7a; margin-top:10px; margin-bottom: 20px; font-size: 1.5em; font-weight: 600;
        }

        /* Service/Horizontal Cards */
        .modern-horizontal-cards {
            display: flex;
            justify-content: space-around;
            padding: 15px 0; /* Reduced side padding as main-content-area has it */
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .modern-h-card {
            flex-basis: calc(33.333% - 20px);
            min-width: 280px;
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: flex-start; /* Align items to the top */
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            color: #333;
            border: 1px solid #e0e8f0;
        }

        .modern-h-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .modern-h-card-img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-right: 18px;
            object-fit: contain;
            flex-shrink: 0; /* Prevent image from shrinking */
        }

        .modern-h-card-content { /* New wrapper for title and features */
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .modern-h-card-title { /* Replaces modern-h-card-label */
            font-size: 1.1em;
            font-weight: bold;
            color: #223a7a;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .modern-h-card-features {
            list-style-type: disc;
            margin: 0;
            padding-left: 20px;
            font-size: 0.9em;
            color: #333;
            line-height: 1.5;
        }

        .modern-h-card-features li {
            margin-bottom: 4px;
        }

        /* .modern-h-card-label and .modern-h-card-arrow are no longer used directly, replaced by title and features */

        /* Health Issues Grid */
        .health-issues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            padding: 15px 0; /* Reduced side padding */
            margin-bottom: 20px;
        }
        .health-issue-item {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: box-shadow 0.2s, transform 0.2s, border-color 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .health-issue-item:hover {
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            border-color: #1976D2;
        }
        .health-issue-icon-placeholder {
            width: 40px;
            height: 40px;
            background-color: #e9f5ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            color: #1976D2;
            font-weight: bold;
            font-size: 0.9em;
        }
        .health-issue-item span {
            font-size: 0.95em;
            color: #333;
            font-weight: 500;
        }

        /* Scrolling Wrappers */
        .scrolling-wrapper-container {
          overflow: hidden;
          padding: 10px 0;
          margin-bottom: 15px;
          position: relative;
        }
        .scrolling-wrapper {
          display: flex;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          padding-bottom: 15px; /* space for scrollbar */
          gap: 15px;
          /* width: max-content; Remove this to allow scrollbar to appear correctly */
        }

        .scrolling-wrapper::-webkit-scrollbar {
          height: 8px;
        }
        .scrolling-wrapper::-webkit-scrollbar-track {
          background: #f0f2f5;
          border-radius: 4px;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb {
          background: #1976D2;
          border-radius: 4px;
          transition: background-color 0.2s;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb:hover {
          background: #145a9e;
        }

        .scrolling-wrapper { /* For Firefox */
          scrollbar-width: thin;
          scrollbar-color: #1976D2 #f0f2f5;
        }
        .doctor-scroll-item, .nurse-scroll-item, .assistant-scroll-item {
            flex: 0 0 auto;
            width: 150px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.07);
            padding: 12px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .doctor-scroll-item:hover, .nurse-scroll-item:hover, .assistant-scroll-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(0,0,0,0.1);
        }
        .doctor-scroll-item img, .nurse-scroll-item img, .assistant-scroll-item img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 8px;
            border: 2px solid #e0e8f0;
        }
        .doctor-name, .nurse-name, .assistant-name {
            display: block;
            font-weight: 600;
            color: #223a7a;
            font-size: 1em;
            margin-bottom: 4px;
        }
        .doctor-description, .nurse-description, .assistant-info {
            font-size: 0.85em;
            color: #555;
            line-height: 1.3;
        }

        /* FAQ Section */
        .faq-list { max-width: 700px; margin: 0 auto 20px auto; }
        .faq-item {
            background-color: #fff; border: 1px solid #e0e0e0;
            border-radius: 6px; margin-bottom: 10px; overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .faq-question {
            width: 100%; background-color: transparent; border: none;
            padding: 15px; text-align: left; font-size: 1.05em; font-weight: 500;
            color: #333; cursor: pointer; display: flex; justify-content: space-between;
            align-items: center;
        }
        .faq-question::after {
            content: '\\276F'; /* Chevron right */
            font-size: 1.2em; color: #1976D2;
            transition: transform 0.3s ease;
        }
        .faq-item.active .faq-question::after { transform: rotate(90deg); }
        .faq-answer {
            padding: 0 15px; max-height: 0; overflow: hidden;
            font-size: 0.95em; color: #555; line-height: 1.6;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }
        .faq-item.active .faq-answer {
            max-height: 300px; /* Increased max-height */
            padding-bottom: 15px;
        }

        /* Provider List Section */
        #provider-list-section {
            padding: 15px;
            background-color: #fff;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        #back-to-services-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-services-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }
        #provider-list-title {
            text-align: center;
            color: #223a7a;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        #provider-list-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        .provider-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #eee;
            box-shadow: 0 3px 7px rgba(0,0,0,0.07);
            display: flex;
            flex-direction: column;
        }
        .provider-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .provider-card-img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid #1976D2;
        }
        .provider-card-info h4 {
            margin: 0 0 4px 0;
            color: #223a7a;
            font-size: 1.1em;
        }
        .provider-card-info p {
            margin: 0;
            font-size: 0.9em;
            color: #555;
        }
        .provider-card-details {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .provider-select-btn {
            margin-top: auto; /* Pushes button to the bottom */
            padding: 10px 15px;
            border-radius: 6px;
            background-color: #1976D2;
            color: white;
            text-align: center;
            font-weight: bold;
            cursor: pointer;
            border: none;
            transition: background-color 0.2s;
        }
        .provider-select-btn:hover {
            background-color: #145a9e;
        }

        /* Patient Details Form for Health Issue */
        #health-issue-patient-form-section .form-group {
            margin-bottom: 15px;
        }
        #health-issue-patient-form-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        #health-issue-patient-form-section input[type=\"text\"],
        #health-issue-patient-form-section input[type=\"number\"],
        #health-issue-patient-form-section input[type=\"tel\"],
        #health-issue-patient-form-section select,
        #health-issue-patient-form-section textarea {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ccc;
            box-sizing: border-box;
            font-size: 1em;
        }
        #health-issue-patient-form-section textarea {
            min-height: 80px;
        }
        #health-issue-patient-form-section button[type=\"submit\"] {
            width: 100%;
            padding: 14px;
            border-radius: 8px;
            border: none;
            background: linear-gradient(90deg, #1976D2, #3ec6e0);
            color: white;
            font-size: 1.05em;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        #health-issue-patient-form-section button[type=\"submit\"]:hover {
            opacity: 0.9;
        }
        #back-to-overview-from-patient-form-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-overview-from-patient-form-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }

        /* Responsive Styles */
        @media screen and (max-width: 768px) {
            .sidebar.open {
                width: 250px;
            }
            .sidebar-content h3 {
                font-size: 1.3em;
            }
            #sidebar-profile-name {
                font-size: 1em;
            }
            .sidebar-content ul li {
                font-size: 0.95em;
                padding: 10px 12px;
            }

            .main-content-area {
                padding: 10px;
                padding-top: 65px; /* Adjust if header height changes */
            }
            .modern-header { padding: 8px 10px; }
            .header-profile-section { flex-shrink: 0; }
            #header-profile-name {
                font-size: 0.9em;
                max-width: 100px; /* Ensure name doesn't push content */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .modern-location { padding: 6px 8px; }
            .modern-location-text { font-size: 0.85em; }
            .location-icon { font-size: 1em; }
            .chevron { font-size: 0.6em; }

            .modern-horizontal-cards { flex-direction: column; padding: 10px 0; gap:10px; }
            .modern-h-card { min-width: unset; width:100%; flex-basis: auto; }
            .modern-h-card-title { font-size: 1em; }

            .section-title { font-size: 1.3em; margin-bottom: 15px; }
            .health-issues-grid { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; padding:10px 0; }
            .health-issue-item { padding: 10px; }
            .health-issue-icon-placeholder { width: 35px; height: 35px; margin-bottom: 8px; }
            .health-issue-item span { font-size: 0.9em; }

            .profile-modal-content { width: 95%; padding: 20px; }
            .profile-modal-content h2 { font-size: 1.4em; }
            #modal-profile-photo-container { width: 100px; height: 100px; }
            .profile-modal-details p { font-size: 0.95em; }

            .location-modal { width: 95%; padding: 20px; }
            .location-modal h2 { font-size: 1.4em; }
            .location-modal #map { height: 180px; }
        }

        /* Fallback for profile icon if SVG fails or for general use */
        .default-profile-icon-fallback {
            display: inline-block;
            width: 24px;
            height: 24px;
            background-color: #fff; /* White background */
            border-radius: 50%;
            text-align: center;
            line-height: 24px; /* Vertically center */
            font-weight: bold;
            color: #1976D2; /* Blue initial */
            font-size: 14px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .main-content-area {
                padding: 15px;
            }

            .modern-horizontal-cards, /* Keep for provider list if it uses this class */
            .modern-vertical-service-cards {
                grid-template-columns: 1fr; /* Fallback for vertical stack */
                display: flex; /* For vertical stack */
                flex-direction: column; /* For vertical stack */
            }

            .health-issues-grid {
                grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
                gap: 15px;
            }

            .modern-h-card,
            .health-issue-item,
            .patient-form {
                padding: 20px;
            }

            .modern-h-card-img {
                width: 50px;
                height: 50px;
            }

            .modern-h-card-title {
                font-size: 18px;
            }

            /* Quick Actions Responsive */
            #quick-actions-container {
                flex-direction: column;
                gap: 10px;
            }
            .quick-action-card {
                padding: 15px;
            }
            .quick-action-icon {
                width: 35px; height: 35px; margin-bottom: 8px;
            }
            .quick-action-text {
                font-size: 1em;
            }
        }

        /* NEW STYLES FOR QUICK ACTIONS AND REVISED LAYOUT */
        #quick-actions-container {
            display: flex;
            flex-direction: column; /* Stack quick actions vertically */
            gap: 10px; /* Space between quick action cards */
            margin-bottom: 30px;
        }

        .quick-action-card {
            background-color: #fff;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: box-shadow 0.2s ease;
            border: 1px solid #e8e8e8;
        }

        .quick-action-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }

        .quick-action-icon {
            width: 40px; /* Adjust as needed */
            height: 40px; /* Adjust as needed */
            margin-right: 15px;
            object-fit: contain;
        }

        .quick-action-text {
            flex-grow: 1;
            font-size: 1.1em;
            font-weight: 500;
            color: #333;
        }

        .quick-action-arrow {
            font-size: 1.2em;
            color: #888;
        }

        /* Styling for Detailed Service Cards (Vertical Stack) */
        .modern-vertical-service-cards {
            display: flex;
            flex-direction: column;
            gap: 15px; /* Space between detailed service cards */
            margin-bottom: 30px;
        }

        /* .modern-h-card styles (used for detailed cards) should already be mostly fine,
           but ensure they work well in a vertical stack.
           The existing .modern-h-card styles will be used. */

        .section-title {
            /* Ensure section titles are styled as per screenshot (prominent, blue-ish) */
            color: #1e3a8a; /* Example blue color */
            font-weight: 600;
            font-size: 1.6em; /* Make it larger */
            margin-top: 30px;
            margin-bottom: 20px;
            text-align: left; /* As per screenshot */
        }

        /* FAQ Styling Update */
        .faq-item .faq-question::after {
            content: '+'; /* Change chevron to plus */
            font-size: 1.5em; /* Make plus icon larger */
            color: #1e3a8a; /* Match title color */
        }

        .faq-item.active .faq-question::after {
            content: '−'; /* Change to minus when active */
            transform: none; /* Remove rotation */
        }

        /* NEW HEADER STYLES */
        .new-main-header {
            background-color: #1e3a8a; /* Dark blue background */
            color: white;
            padding: 10px 15px;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .user-info-line {
            display: flex;
            align-items: center;
            margin-bottom: 8px; /* Space between user line and location line */
        }
        .user-avatar-icon {
            width: 32px; /* Adjust size */
            height: 32px; /* Adjust size */
            margin-right: 10px;
            cursor: pointer;
            border-radius: 50%; /* If the SVG itself isn't round */
        }
        .user-name-display {
            font-weight: 500;
            font-size: 1.1em;
        }
        .location-line {
            display: flex;
            align-items: center;
            cursor: pointer;
            background-color: white; /* White background for location bar */
            color: #333; /* Dark text for location */
            padding: 8px 12px;
            border-radius: 8px;
        }
        .location-pin-icon {
            margin-right: 8px;
            font-size: 1.1em;
        }
        .location-address-text {
            font-size: 0.95em;
            font-weight: 500;
            margin-right: auto; /* Pushes chevron to the right */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px; /* Adjust as needed */
        }
        .location-chevron-icon {
            font-size: 0.8em;
        }
        /* END NEW HEADER STYLES */

        /* Location Modal Styles (ensure they work with new header trigger) */
        .location-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.65); /* Darker overlay */
            display: none; /* Hidden by default */
            align-items: center; justify-content: center;
            z-index: 2000;
            opacity: 0; transition: opacity 0.3s ease-in-out;
        }
        .location-overlay.active {
            display: flex;
            opacity: 1;
        }
        .location-modal {
            background-color: white; padding: 30px; border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            width: 90%; max-width: 550px;
            transform: scale(0.9); transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Pop effect */
            position: relative;
        }
        .location-overlay.active .location-modal { transform: scale(1); }

        .close-location-modal {
            position: absolute; top: 12px; right: 18px; font-size: 32px; /* Larger */
            font-weight: bold; color: #888; cursor: pointer; transition: color 0.2s, transform 0.2s;
            line-height: 1; /* Ensure proper alignment */
        }
        .close-location-modal:hover { color: #333; transform: rotate(90deg); }

        .location-modal h2 {
            color: #223a7a; font-size: 1.6em; margin-top: 0; margin-bottom: 25px; text-align: center;
            font-weight: 600;
        }
        .location-modal #map {
            height: 200px; /* Fixed height for the map */
            margin-bottom: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        #selected-address {
            font-weight: 500;
            font-size: 0.95em;
            color: #333;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 6px;
            border: 1px solid #eee;
            min-height: 1.5em; /* Ensure it has some height even when empty */
            text-align: center;
        }
        .location-modal input[type="text"] {
            width: 100%; padding: 14px; margin-bottom: 18px;
            border: 1px solid #ccc; border-radius: 8px; box-sizing: border-box; font-size: 1em;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .location-modal input[type="text"]:focus {
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.15);
            outline: none;
        }
        .location-modal button {
            width: 100%; padding: 14px; border-radius: 8px; border: none;
            font-size: 1.05em; font-weight: bold; cursor: pointer; transition: background-color 0.2s, transform 0.1s;
        }
        .location-modal button:active {
            transform: scale(0.98);
        }
        #use-current-location {
            background-color: #e9f5ff; color: #1976D2; margin-bottom: 12px;
            border: 1px solid #1976D2;
        }
        #use-current-location:hover { background-color: #d4e9ff; }
        #location-submit {
            background: linear-gradient(90deg, #1976D2, #3ec6e0); color: white;
        }
        #location-submit:disabled { background: #ccc; cursor: not-allowed; }
        #location-submit:hover:not(:disabled) { opacity:0.9; }
        .location-error {
            color: #e74c3c; font-size: 0.9em; text-align: center; margin-top: 12px; min-height: 1.2em;
            font-weight: 500;
        }

        /* Main content padding */
        .main-content-area {
            padding: 15px;
            padding-top: 75px; /* Adjust if header height changes */
        }

        .section-title {
          text-align: center; color: #223a7a; margin-top:10px; margin-bottom: 20px; font-size: 1.5em; font-weight: 600;
        }

        /* Service/Horizontal Cards */
        .modern-horizontal-cards {
            display: flex;
            justify-content: space-around;
            padding: 15px 0; /* Reduced side padding as main-content-area has it */
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .modern-h-card {
            flex-basis: calc(33.333% - 20px);
            min-width: 280px;
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: flex-start; /* Align items to the top */
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            color: #333;
            border: 1px solid #e0e8f0;
        }

        .modern-h-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .modern-h-card-img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-right: 18px;
            object-fit: contain;
            flex-shrink: 0; /* Prevent image from shrinking */
        }

        .modern-h-card-content { /* New wrapper for title and features */
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .modern-h-card-title { /* Replaces modern-h-card-label */
            font-size: 1.1em;
            font-weight: bold;
            color: #223a7a;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .modern-h-card-features {
            list-style-type: disc;
            margin: 0;
            padding-left: 20px;
            font-size: 0.9em;
            color: #333;
            line-height: 1.5;
        }

        .modern-h-card-features li {
            margin-bottom: 4px;
        }

        /* .modern-h-card-label and .modern-h-card-arrow are no longer used directly, replaced by title and features */

        /* Health Issues Grid */
        .health-issues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            padding: 15px 0; /* Reduced side padding */
            margin-bottom: 20px;
        }
        .health-issue-item {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: box-shadow 0.2s, transform 0.2s, border-color 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .health-issue-item:hover {
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            border-color: #1976D2;
        }
        .health-issue-icon-placeholder {
            width: 40px;
            height: 40px;
            background-color: #e9f5ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            color: #1976D2;
            font-weight: bold;
            font-size: 0.9em;
        }
        .health-issue-item span {
            font-size: 0.95em;
            color: #333;
            font-weight: 500;
        }

        /* Scrolling Wrappers */
        .scrolling-wrapper-container {
          overflow: hidden;
          padding: 10px 0;
          margin-bottom: 15px;
          position: relative;
        }
        .scrolling-wrapper {
          display: flex;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          padding-bottom: 15px; /* space for scrollbar */
          gap: 15px;
          /* width: max-content; Remove this to allow scrollbar to appear correctly */
        }

        .scrolling-wrapper::-webkit-scrollbar {
          height: 8px;
        }
        .scrolling-wrapper::-webkit-scrollbar-track {
          background: #f0f2f5;
          border-radius: 4px;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb {
          background: #1976D2;
          border-radius: 4px;
          transition: background-color 0.2s;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb:hover {
          background: #145a9e;
        }

        .scrolling-wrapper { /* For Firefox */
          scrollbar-width: thin;
          scrollbar-color: #1976D2 #f0f2f5;
        }
        .doctor-scroll-item, .nurse-scroll-item, .assistant-scroll-item {
            flex: 0 0 auto;
            width: 150px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.07);
            padding: 12px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .doctor-scroll-item:hover, .nurse-scroll-item:hover, .assistant-scroll-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(0,0,0,0.1);
        }
        .doctor-scroll-item img, .nurse-scroll-item img, .assistant-scroll-item img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 8px;
            border: 2px solid #e0e8f0;
        }
        .doctor-name, .nurse-name, .assistant-name {
            display: block;
            font-weight: 600;
            color: #223a7a;
            font-size: 1em;
            margin-bottom: 4px;
        }
        .doctor-description, .nurse-description, .assistant-info {
            font-size: 0.85em;
            color: #555;
            line-height: 1.3;
        }

        /* FAQ Section */
        .faq-list { max-width: 700px; margin: 0 auto 20px auto; }
        .faq-item {
            background-color: #fff; border: 1px solid #e0e0e0;
            border-radius: 6px; margin-bottom: 10px; overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .faq-question {
            width: 100%; background-color: transparent; border: none;
            padding: 15px; text-align: left; font-size: 1.05em; font-weight: 500;
            color: #333; cursor: pointer; display: flex; justify-content: space-between;
            align-items: center;
        }
        .faq-question::after {
            content: '\\276F'; /* Chevron right */
            font-size: 1.2em; color: #1976D2;
            transition: transform 0.3s ease;
        }
        .faq-item.active .faq-question::after { transform: rotate(90deg); }
        .faq-answer {
            padding: 0 15px; max-height: 0; overflow: hidden;
            font-size: 0.95em; color: #555; line-height: 1.6;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }
        .faq-item.active .faq-answer {
            max-height: 300px; /* Increased max-height */
            padding-bottom: 15px;
        }

        /* Provider List Section */
        #provider-list-section {
            padding: 15px;
            background-color: #fff;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        #back-to-services-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-services-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }
        #provider-list-title {
            text-align: center;
            color: #223a7a;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        #provider-list-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        .provider-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #eee;
            box-shadow: 0 3px 7px rgba(0,0,0,0.07);
            display: flex;
            flex-direction: column;
        }
        .provider-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .provider-card-img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid #1976D2;
        }
        .provider-card-info h4 {
            margin: 0 0 4px 0;
            color: #223a7a;
            font-size: 1.1em;
        }
        .provider-card-info p {
            margin: 0;
            font-size: 0.9em;
            color: #555;
        }
        .provider-card-details {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .provider-select-btn {
            margin-top: auto; /* Pushes button to the bottom */
            padding: 10px 15px;
            border-radius: 6px;
            background-color: #1976D2;
            color: white;
            text-align: center;
            font-weight: bold;
            cursor: pointer;
            border: none;
            transition: background-color 0.2s;
        }
        .provider-select-btn:hover {
            background-color: #145a9e;
        }

        /* Patient Details Form for Health Issue */
        #health-issue-patient-form-section .form-group {
            margin-bottom: 15px;
        }
        #health-issue-patient-form-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        #health-issue-patient-form-section input[type=\"text\"],
        #health-issue-patient-form-section input[type=\"number\"],
        #health-issue-patient-form-section input[type=\"tel\"],
        #health-issue-patient-form-section select,
        #health-issue-patient-form-section textarea {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ccc;
            box-sizing: border-box;
            font-size: 1em;
        }
        #health-issue-patient-form-section textarea {
            min-height: 80px;
        }
        #health-issue-patient-form-section button[type=\"submit\"] {
            width: 100%;
            padding: 14px;
            border-radius: 8px;
            border: none;
            background: linear-gradient(90deg, #1976D2, #3ec6e0);
            color: white;
            font-size: 1.05em;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        #health-issue-patient-form-section button[type=\"submit\"]:hover {
            opacity: 0.9;
        }
        #back-to-overview-from-patient-form-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-overview-from-patient-form-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }

        /* Responsive Styles */
        @media screen and (max-width: 768px) {
            .sidebar.open {
                width: 250px;
            }
            .sidebar-content h3 {
                font-size: 1.3em;
            }
            #sidebar-profile-name {
                font-size: 1em;
            }
            .sidebar-content ul li {
                font-size: 0.95em;
                padding: 10px 12px;
            }

            .main-content-area {
                padding: 10px;
                padding-top: 65px; /* Adjust if header height changes */
            }
            .modern-header { padding: 8px 10px; }
            .header-profile-section { flex-shrink: 0; }
            #header-profile-name {
                font-size: 0.9em;
                max-width: 100px; /* Ensure name doesn't push content */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .modern-location { padding: 6px 8px; }
            .modern-location-text { font-size: 0.85em; }
            .location-icon { font-size: 1em; }
            .chevron { font-size: 0.6em; }

            .modern-horizontal-cards { flex-direction: column; padding: 10px 0; gap:10px; }
            .modern-h-card { min-width: unset; width:100%; flex-basis: auto; }
            .modern-h-card-title { font-size: 1em; }

            .section-title { font-size: 1.3em; margin-bottom: 15px; }
            .health-issues-grid { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; padding:10px 0; }
            .health-issue-item { padding: 10px; }
            .health-issue-icon-placeholder { width: 35px; height: 35px; margin-bottom: 8px; }
            .health-issue-item span { font-size: 0.9em; }

            .profile-modal-content { width: 95%; padding: 20px; }
            .profile-modal-content h2 { font-size: 1.4em; }
            #modal-profile-photo-container { width: 100px; height: 100px; }
            .profile-modal-details p { font-size: 0.95em; }

            .location-modal { width: 95%; padding: 20px; }
            .location-modal h2 { font-size: 1.4em; }
            .location-modal #map { height: 180px; }
        }

        /* Fallback for profile icon if SVG fails or for general use */
        .default-profile-icon-fallback {
            display: inline-block;
            width: 24px;
            height: 24px;
            background-color: #fff; /* White background */
            border-radius: 50%;
            text-align: center;
            line-height: 24px; /* Vertically center */
            font-weight: bold;
            color: #1976D2; /* Blue initial */
            font-size: 14px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .main-content-area {
                padding: 15px;
            }

            .modern-horizontal-cards, /* Keep for provider list if it uses this class */
            .modern-vertical-service-cards {
                grid-template-columns: 1fr; /* Fallback for vertical stack */
                display: flex; /* For vertical stack */
                flex-direction: column; /* For vertical stack */
            }

            .health-issues-grid {
                grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
                gap: 15px;
            }

            .modern-h-card,
            .health-issue-item,
            .patient-form {
                padding: 20px;
            }

            .modern-h-card-img {
                width: 50px;
                height: 50px;
            }

            .modern-h-card-title {
                font-size: 18px;
            }

            /* Quick Actions Responsive */
            #quick-actions-container {
                flex-direction: column;
                gap: 10px;
            }
            .quick-action-card {
                padding: 15px;
            }
            .quick-action-icon {
                width: 35px; height: 35px; margin-bottom: 8px;
            }
            .quick-action-text {
                font-size: 1em;
            }
        }

        /* NEW STYLES FOR QUICK ACTIONS AND REVISED LAYOUT */
        #quick-actions-container {
            display: flex;
            flex-direction: column; /* Stack quick actions vertically */
            gap: 10px; /* Space between quick action cards */
            margin-bottom: 30px;
        }

        .quick-action-card {
            background-color: #fff;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: box-shadow 0.2s ease;
            border: 1px solid #e8e8e8;
        }

        .quick-action-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }

        .quick-action-icon {
            width: 40px; /* Adjust as needed */
            height: 40px; /* Adjust as needed */
            margin-right: 15px;
            object-fit: contain;
        }

        .quick-action-text {
            flex-grow: 1;
            font-size: 1.1em;
            font-weight: 500;
            color: #333;
        }

        .quick-action-arrow {
            font-size: 1.2em;
            color: #888;
        }

        /* Styling for Detailed Service Cards (Vertical Stack) */
        .modern-vertical-service-cards {
            display: flex;
            flex-direction: column;
            gap: 15px; /* Space between detailed service cards */
            margin-bottom: 30px;
        }

        /* .modern-h-card styles (used for detailed cards) should already be mostly fine,
           but ensure they work well in a vertical stack.
           The existing .modern-h-card styles will be used. */

        .section-title {
            /* Ensure section titles are styled as per screenshot (prominent, blue-ish) */
            color: #1e3a8a; /* Example blue color */
            font-weight: 600;
            font-size: 1.6em; /* Make it larger */
            margin-top: 30px;
            margin-bottom: 20px;
            text-align: left; /* As per screenshot */
        }

        /* FAQ Styling Update */
        .faq-item .faq-question::after {
            content: '+'; /* Change chevron to plus */
            font-size: 1.5em; /* Make plus icon larger */
            color: #1e3a8a; /* Match title color */
        }

        .faq-item.active .faq-question::after {
            content: '−'; /* Change to minus when active */
            transform: none; /* Remove rotation */
        }

        /* NEW HEADER STYLES */
        .new-main-header {
            background-color: #1e3a8a; /* Dark blue background */
            color: white;
            padding: 10px 15px;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .user-info-line {
            display: flex;
            align-items: center;
            margin-bottom: 8px; /* Space between user line and location line */
        }
        .user-avatar-icon {
            width: 32px; /* Adjust size */
            height: 32px; /* Adjust size */
            margin-right: 10px;
            cursor: pointer;
            border-radius: 50%; /* If the SVG itself isn't round */
        }
        .user-name-display {
            font-weight: 500;
            font-size: 1.1em;
        }
        .location-line {
            display: flex;
            align-items: center;
            cursor: pointer;
            background-color: white; /* White background for location bar */
            color: #333; /* Dark text for location */
            padding: 8px 12px;
            border-radius: 8px;
        }
        .location-pin-icon {
            margin-right: 8px;
            font-size: 1.1em;
        }
        .location-address-text {
            font-size: 0.95em;
            font-weight: 500;
            margin-right: auto; /* Pushes chevron to the right */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px; /* Adjust as needed */
        }
        .location-chevron-icon {
            font-size: 0.8em;
        }
        /* END NEW HEADER STYLES */

        /* Location Modal Styles (ensure they work with new header trigger) */
        .location-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.65); /* Darker overlay */
            display: none; /* Hidden by default */
            align-items: center; justify-content: center;
            z-index: 2000;
            opacity: 0; transition: opacity 0.3s ease-in-out;
        }
        .location-overlay.active {
            display: flex;
            opacity: 1;
        }
        .location-modal {
            background-color: white; padding: 30px; border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            width: 90%; max-width: 550px;
            transform: scale(0.9); transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Pop effect */
            position: relative;
        }
        .location-overlay.active .location-modal { transform: scale(1); }

        .close-location-modal {
            position: absolute; top: 12px; right: 18px; font-size: 32px; /* Larger */
            font-weight: bold; color: #888; cursor: pointer; transition: color 0.2s, transform 0.2s;
            line-height: 1; /* Ensure proper alignment */
        }
        .close-location-modal:hover { color: #333; transform: rotate(90deg); }

        .location-modal h2 {
            color: #223a7a; font-size: 1.6em; margin-top: 0; margin-bottom: 25px; text-align: center;
            font-weight: 600;
        }
        .location-modal #map {
            height: 200px; /* Fixed height for the map */
            margin-bottom: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        #selected-address {
            font-weight: 500;
            font-size: 0.95em;
            color: #333;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 6px;
            border: 1px solid #eee;
            min-height: 1.5em; /* Ensure it has some height even when empty */
            text-align: center;
        }
        .location-modal input[type="text"] {
            width: 100%; padding: 14px; margin-bottom: 18px;
            border: 1px solid #ccc; border-radius: 8px; box-sizing: border-box; font-size: 1em;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .location-modal input[type="text"]:focus {
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.15);
            outline: none;
        }
        .location-modal button {
            width: 100%; padding: 14px; border-radius: 8px; border: none;
            font-size: 1.05em; font-weight: bold; cursor: pointer; transition: background-color 0.2s, transform 0.1s;
        }
        .location-modal button:active {
            transform: scale(0.98);
        }
        #use-current-location {
            background-color: #e9f5ff; color: #1976D2; margin-bottom: 12px;
            border: 1px solid #1976D2;
        }
        #use-current-location:hover { background-color: #d4e9ff; }
        #location-submit {
            background: linear-gradient(90deg, #1976D2, #3ec6e0); color: white;
        }
        #location-submit:disabled { background: #ccc; cursor: not-allowed; }
        #location-submit:hover:not(:disabled) { opacity:0.9; }
        .location-error {
            color: #e74c3c; font-size: 0.9em; text-align: center; margin-top: 12px; min-height: 1.2em;
            font-weight: 500;
        }

        /* Main content padding */
        .main-content-area {
            padding: 15px;
            padding-top: 75px; /* Adjust if header height changes */
        }

        .section-title {
          text-align: center; color: #223a7a; margin-top:10px; margin-bottom: 20px; font-size: 1.5em; font-weight: 600;
        }

        /* Service/Horizontal Cards */
        .modern-horizontal-cards {
            display: flex;
            justify-content: space-around;
            padding: 15px 0; /* Reduced side padding as main-content-area has it */
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .modern-h-card {
            flex-basis: calc(33.333% - 20px);
            min-width: 280px;
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: flex-start; /* Align items to the top */
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            color: #333;
            border: 1px solid #e0e8f0;
        }

        .modern-h-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .modern-h-card-img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-right: 18px;
            object-fit: contain;
            flex-shrink: 0; /* Prevent image from shrinking */
        }

        .modern-h-card-content { /* New wrapper for title and features */
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .modern-h-card-title { /* Replaces modern-h-card-label */
            font-size: 1.1em;
            font-weight: bold;
            color: #223a7a;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .modern-h-card-features {
            list-style-type: disc;
            margin: 0;
            padding-left: 20px;
            font-size: 0.9em;
            color: #333;
            line-height: 1.5;
        }

        .modern-h-card-features li {
            margin-bottom: 4px;
        }

        /* .modern-h-card-label and .modern-h-card-arrow are no longer used directly, replaced by title and features */

        /* Health Issues Grid */
        .health-issues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            padding: 15px 0; /* Reduced side padding */
            margin-bottom: 20px;
        }
        .health-issue-item {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: box-shadow 0.2s, transform 0.2s, border-color 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .health-issue-item:hover {
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            border-color: #1976D2;
        }
        .health-issue-icon-placeholder {
            width: 40px;
            height: 40px;
            background-color: #e9f5ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            color: #1976D2;
            font-weight: bold;
            font-size: 0.9em;
        }
        .health-issue-item span {
            font-size: 0.95em;
            color: #333;
            font-weight: 500;
        }

        /* Scrolling Wrappers */
        .scrolling-wrapper-container {
          overflow: hidden;
          padding: 10px 0;
          margin-bottom: 15px;
          position: relative;
        }
        .scrolling-wrapper {
          display: flex;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          padding-bottom: 15px; /* space for scrollbar */
          gap: 15px;
          /* width: max-content; Remove this to allow scrollbar to appear correctly */
        }

        .scrolling-wrapper::-webkit-scrollbar {
          height: 8px;
        }
        .scrolling-wrapper::-webkit-scrollbar-track {
          background: #f0f2f5;
          border-radius: 4px;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb {
          background: #1976D2;
          border-radius: 4px;
          transition: background-color 0.2s;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb:hover {
          background: #145a9e;
        }

        .scrolling-wrapper { /* For Firefox */
          scrollbar-width: thin;
          scrollbar-color: #1976D2 #f0f2f5;
        }
        .doctor-scroll-item, .nurse-scroll-item, .assistant-scroll-item {
            flex: 0 0 auto;
            width: 150px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.07);
            padding: 12px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .doctor-scroll-item:hover, .nurse-scroll-item:hover, .assistant-scroll-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(0,0,0,0.1);
        }
        .doctor-scroll-item img, .nurse-scroll-item img, .assistant-scroll-item img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 8px;
            border: 2px solid #e0e8f0;
        }
        .doctor-name, .nurse-name, .assistant-name {
            display: block;
            font-weight: 600;
            color: #223a7a;
            font-size: 1em;
            margin-bottom: 4px;
        }
        .doctor-description, .nurse-description, .assistant-info {
            font-size: 0.85em;
            color: #555;
            line-height: 1.3;
        }

        /* FAQ Section */
        .faq-list { max-width: 700px; margin: 0 auto 20px auto; }
        .faq-item {
            background-color: #fff; border: 1px solid #e0e0e0;
            border-radius: 6px; margin-bottom: 10px; overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .faq-question {
            width: 100%; background-color: transparent; border: none;
            padding: 15px; text-align: left; font-size: 1.05em; font-weight: 500;
            color: #333; cursor: pointer; display: flex; justify-content: space-between;
            align-items: center;
        }
        .faq-question::after {
            content: '\\276F'; /* Chevron right */
            font-size: 1.2em; color: #1976D2;
            transition: transform 0.3s ease;
        }
        .faq-item.active .faq-question::after { transform: rotate(90deg); }
        .faq-answer {
            padding: 0 15px; max-height: 0; overflow: hidden;
            font-size: 0.95em; color: #555; line-height: 1.6;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }
        .faq-item.active .faq-answer {
            max-height: 300px; /* Increased max-height */
            padding-bottom: 15px;
        }

        /* Provider List Section */
        #provider-list-section {
            padding: 15px;
            background-color: #fff;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        #back-to-services-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-services-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }
        #provider-list-title {
            text-align: center;
            color: #223a7a;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        #provider-list-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        .provider-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #eee;
            box-shadow: 0 3px 7px rgba(0,0,0,0.07);
            display: flex;
            flex-direction: column;
        }
        .provider-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .provider-card-img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid #1976D2;
        }
        .provider-card-info h4 {
            margin: 0 0 4px 0;
            color: #223a7a;
            font-size: 1.1em;
        }
        .provider-card-info p {
            margin: 0;
            font-size: 0.9em;
            color: #555;
        }
        .provider-card-details {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .provider-select-btn {
            margin-top: auto; /* Pushes button to the bottom */
            padding: 10px 15px;
            border-radius: 6px;
            background-color: #1976D2;
            color: white;
            text-align: center;
            font-weight: bold;
            cursor: pointer;
            border: none;
            transition: background-color 0.2s;
        }
        .provider-select-btn:hover {
            background-color: #145a9e;
        }

        /* Patient Details Form for Health Issue */
        #health-issue-patient-form-section .form-group {
            margin-bottom: 15px;
        }
        #health-issue-patient-form-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        #health-issue-patient-form-section input[type=\"text\"],
        #health-issue-patient-form-section input[type=\"number\"],
        #health-issue-patient-form-section input[type=\"tel\"],
        #health-issue-patient-form-section select,
        #health-issue-patient-form-section textarea {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ccc;
            box-sizing: border-box;
            font-size: 1em;
        }
        #health-issue-patient-form-section textarea {
            min-height: 80px;
        }
        #health-issue-patient-form-section button[type=\"submit\"] {
            width: 100%;
            padding: 14px;
            border-radius: 8px;
            border: none;
            background: linear-gradient(90deg, #1976D2, #3ec6e0);
            color: white;
            font-size: 1.05em;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        #health-issue-patient-form-section button[type=\"submit\"]:hover {
            opacity: 0.9;
        }
        #back-to-overview-from-patient-form-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-overview-from-patient-form-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }

        /* Responsive Styles */
        @media screen and (max-width: 768px) {
            .sidebar.open {
                width: 250px;
            }
            .sidebar-content h3 {
                font-size: 1.3em;
            }
            #sidebar-profile-name {
                font-size: 1em;
            }
            .sidebar-content ul li {
                font-size: 0.95em;
                padding: 10px 12px;
            }

            .main-content-area {
                padding: 10px;
                padding-top: 65px; /* Adjust if header height changes */
            }
            .modern-header { padding: 8px 10px; }
            .header-profile-section { flex-shrink: 0; }
            #header-profile-name {
                font-size: 0.9em;
                max-width: 100px; /* Ensure name doesn't push content */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .modern-location { padding: 6px 8px; }
            .modern-location-text { font-size: 0.85em; }
            .location-icon { font-size: 1em; }
            .chevron { font-size: 0.6em; }

            .modern-horizontal-cards { flex-direction: column; padding: 10px 0; gap:10px; }
            .modern-h-card { min-width: unset; width:100%; flex-basis: auto; }
            .modern-h-card-title { font-size: 1em; }

            .section-title { font-size: 1.3em; margin-bottom: 15px; }
            .health-issues-grid { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; padding:10px 0; }
            .health-issue-item { padding: 10px; }
            .health-issue-icon-placeholder { width: 35px; height: 35px; margin-bottom: 8px; }
            .health-issue-item span { font-size: 0.9em; }

            .profile-modal-content { width: 95%; padding: 20px; }
            .profile-modal-content h2 { font-size: 1.4em; }
            #modal-profile-photo-container { width: 100px; height: 100px; }
            .profile-modal-details p { font-size: 0.95em; }

            .location-modal { width: 95%; padding: 20px; }
            .location-modal h2 { font-size: 1.4em; }
            .location-modal #map { height: 180px; }
        }

        /* Fallback for profile icon if SVG fails or for general use */
        .default-profile-icon-fallback {
            display: inline-block;
            width: 24px;
            height: 24px;
            background-color: #fff; /* White background */
            border-radius: 50%;
            text-align: center;
            line-height: 24px; /* Vertically center */
            font-weight: bold;
            color: #1976D2; /* Blue initial */
            font-size: 14px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .main-content-area {
                padding: 15px;
            }

            .modern-horizontal-cards, /* Keep for provider list if it uses this class */
            .modern-vertical-service-cards {
                grid-template-columns: 1fr; /* Fallback for vertical stack */
                display: flex; /* For vertical stack */
                flex-direction: column; /* For vertical stack */
            }

            .health-issues-grid {
                grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
                gap: 15px;
            }

            .modern-h-card,
            .health-issue-item,
            .patient-form {
                padding: 20px;
            }

            .modern-h-card-img {
                width: 50px;
                height: 50px;
            }

            .modern-h-card-title {
                font-size: 18px;
            }

            /* Quick Actions Responsive */
            #quick-actions-container {
                flex-direction: column;
                gap: 10px;
            }
            .quick-action-card {
                padding: 15px;
            }
            .quick-action-icon {
                width: 35px; height: 35px; margin-bottom: 8px;
            }
            .quick-action-text {
                font-size: 1em;
            }
        }

        /* NEW STYLES FOR QUICK ACTIONS AND REVISED LAYOUT */
        #quick-actions-container {
            display: flex;
            flex-direction: column; /* Stack quick actions vertically */
            gap: 10px; /* Space between quick action cards */
            margin-bottom: 30px;
        }

        .quick-action-card {
            background-color: #fff;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: box-shadow 0.2s ease;
            border: 1px solid #e8e8e8;
        }

        .quick-action-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }

        .quick-action-icon {
            width: 40px; /* Adjust as needed */
            height: 40px; /* Adjust as needed */
            margin-right: 15px;
            object-fit: contain;
        }

        .quick-action-text {
            flex-grow: 1;
            font-size: 1.1em;
            font-weight: 500;
            color: #333;
        }

        .quick-action-arrow {
            font-size: 1.2em;
            color: #888;
        }

        /* Styling for Detailed Service Cards (Vertical Stack) */
        .modern-vertical-service-cards {
            display: flex;
            flex-direction: column;
            gap: 15px; /* Space between detailed service cards */
            margin-bottom: 30px;
        }

        /* .modern-h-card styles (used for detailed cards) should already be mostly fine,
           but ensure they work well in a vertical stack.
           The existing .modern-h-card styles will be used. */

        .section-title {
            /* Ensure section titles are styled as per screenshot (prominent, blue-ish) */
            color: #1e3a8a; /* Example blue color */
            font-weight: 600;
            font-size: 1.6em; /* Make it larger */
            margin-top: 30px;
            margin-bottom: 20px;
            text-align: left; /* As per screenshot */
        }

        /* FAQ Styling Update */
        .faq-item .faq-question::after {
            content: '+'; /* Change chevron to plus */
            font-size: 1.5em; /* Make plus icon larger */
            color: #1e3a8a; /* Match title color */
        }

        .faq-item.active .faq-question::after {
            content: '−'; /* Change to minus when active */
            transform: none; /* Remove rotation */
        }

        /* NEW HEADER STYLES */
        .new-main-header {
            background-color: #1e3a8a; /* Dark blue background */
            color: white;
            padding: 10px 15px;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .user-info-line {
            display: flex;
            align-items: center;
            margin-bottom: 8px; /* Space between user line and location line */
        }
        .user-avatar-icon {
            width: 32px; /* Adjust size */
            height: 32px; /* Adjust size */
            margin-right: 10px;
            cursor: pointer;
            border-radius: 50%; /* If the SVG itself isn't round */
        }
        .user-name-display {
            font-weight: 500;
            font-size: 1.1em;
        }
        .location-line {
            display: flex;
            align-items: center;
            cursor: pointer;
            background-color: white; /* White background for location bar */
            color: #333; /* Dark text for location */
            padding: 8px 12px;
            border-radius: 8px;
        }
        .location-pin-icon {
            margin-right: 8px;
            font-size: 1.1em;
        }
        .location-address-text {
            font-size: 0.95em;
            font-weight: 500;
            margin-right: auto; /* Pushes chevron to the right */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px; /* Adjust as needed */
        }
        .location-chevron-icon {
            font-size: 0.8em;
        }
        /* END NEW HEADER STYLES */

        /* Location Modal Styles (ensure they work with new header trigger) */
        .location-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.65); /* Darker overlay */
            display: none; /* Hidden by default */
            align-items: center; justify-content: center;
            z-index: 2000;
            opacity: 0; transition: opacity 0.3s ease-in-out;
        }
        .location-overlay.active {
            display: flex;
            opacity: 1;
        }
        .location-modal {
            background-color: white; padding: 30px; border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            width: 90%; max-width: 550px;
            transform: scale(0.9); transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Pop effect */
            position: relative;
        }
        .location-overlay.active .location-modal { transform: scale(1); }

        .close-location-modal {
            position: absolute; top: 12px; right: 18px; font-size: 32px; /* Larger */
            font-weight: bold; color: #888; cursor: pointer; transition: color 0.2s, transform 0.2s;
            line-height: 1; /* Ensure proper alignment */
        }
        .close-location-modal:hover { color: #333; transform: rotate(90deg); }

        .location-modal h2 {
            color: #223a7a; font-size: 1.6em; margin-top: 0; margin-bottom: 25px; text-align: center;
            font-weight: 600;
        }
        .location-modal #map {
            height: 200px; /* Fixed height for the map */
            margin-bottom: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        #selected-address {
            font-weight: 500;
            font-size: 0.95em;
            color: #333;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 6px;
            border: 1px solid #eee;
            min-height: 1.5em; /* Ensure it has some height even when empty */
            text-align: center;
        }
        .location-modal input[type="text"] {
            width: 100%; padding: 14px; margin-bottom: 18px;
            border: 1px solid #ccc; border-radius: 8px; box-sizing: border-box; font-size: 1em;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .location-modal input[type="text"]:focus {
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.15);
            outline: none;
        }
        .location-modal button {
            width: 100%; padding: 14px; border-radius: 8px; border: none;
            font-size: 1.05em; font-weight: bold; cursor: pointer; transition: background-color 0.2s, transform 0.1s;
        }
        .location-modal button:active {
            transform: scale(0.98);
        }
        #use-current-location {
            background-color: #e9f5ff; color: #1976D2; margin-bottom: 12px;
            border: 1px solid #1976D2;
        }
        #use-current-location:hover { background-color: #d4e9ff; }
        #location-submit {
            background: linear-gradient(90deg, #1976D2, #3ec6e0); color: white;
        }
        #location-submit:disabled { background: #ccc; cursor: not-allowed; }
        #location-submit:hover:not(:disabled) { opacity:0.9; }
        .location-error {
            color: #e74c3c; font-size: 0.9em; text-align: center; margin-top: 12px; min-height: 1.2em;
            font-weight: 500;
        }

        /* Main content padding */
        .main-content-area {
            padding: 15px;
            padding-top: 75px; /* Adjust if header height changes */
        }

        .section-title {
          text-align: center; color: #223a7a; margin-top:10px; margin-bottom: 20px; font-size: 1.5em; font-weight: 600;
        }

        /* Service/Horizontal Cards */
        .modern-horizontal-cards {
            display: flex;
            justify-content: space-around;
            padding: 15px 0; /* Reduced side padding as main-content-area has it */
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .modern-h-card {
            flex-basis: calc(33.333% - 20px);
            min-width: 280px;
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: flex-start; /* Align items to the top */
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            color: #333;
            border: 1px solid #e0e8f0;
        }

        .modern-h-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .modern-h-card-img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-right: 18px;
            object-fit: contain;
            flex-shrink: 0; /* Prevent image from shrinking */
        }

        .modern-h-card-content { /* New wrapper for title and features */
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .modern-h-card-title { /* Replaces modern-h-card-label */
            font-size: 1.1em;
            font-weight: bold;
            color: #223a7a;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .modern-h-card-features {
            list-style-type: disc;
            margin: 0;
            padding-left: 20px;
            font-size: 0.9em;
            color: #333;
            line-height: 1.5;
        }

        .modern-h-card-features li {
            margin-bottom: 4px;
        }

        /* .modern-h-card-label and .modern-h-card-arrow are no longer used directly, replaced by title and features */

        /* Health Issues Grid */
        .health-issues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            padding: 15px 0; /* Reduced side padding */
            margin-bottom: 20px;
        }
        .health-issue-item {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: box-shadow 0.2s, transform 0.2s, border-color 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .health-issue-item:hover {
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            border-color: #1976D2;
        }
        .health-issue-icon-placeholder {
            width: 40px;
            height: 40px;
            background-color: #e9f5ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            color: #1976D2;
            font-weight: bold;
            font-size: 0.9em;
        }
        .health-issue-item span {
            font-size: 0.95em;
            color: #333;
            font-weight: 500;
        }

        /* Scrolling Wrappers */
        .scrolling-wrapper-container {
          overflow: hidden;
          padding: 10px 0;
          margin-bottom: 15px;
          position: relative;
        }
        .scrolling-wrapper {
          display: flex;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          padding-bottom: 15px; /* space for scrollbar */
          gap: 15px;
          /* width: max-content; Remove this to allow scrollbar to appear correctly */
        }

        .scrolling-wrapper::-webkit-scrollbar {
          height: 8px;
        }
        .scrolling-wrapper::-webkit-scrollbar-track {
          background: #f0f2f5;
          border-radius: 4px;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb {
          background: #1976D2;
          border-radius: 4px;
          transition: background-color 0.2s;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb:hover {
          background: #145a9e;
        }

        .scrolling-wrapper { /* For Firefox */
          scrollbar-width: thin;
          scrollbar-color: #1976D2 #f0f2f5;
        }
        .doctor-scroll-item, .nurse-scroll-item, .assistant-scroll-item {
            flex: 0 0 auto;
            width: 150px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.07);
            padding: 12px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .doctor-scroll-item:hover, .nurse-scroll-item:hover, .assistant-scroll-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(0,0,0,0.1);
        }
        .doctor-scroll-item img, .nurse-scroll-item img, .assistant-scroll-item img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 8px;
            border: 2px solid #e0e8f0;
        }
        .doctor-name, .nurse-name, .assistant-name {
            display: block;
            font-weight: 600;
            color: #223a7a;
            font-size: 1em;
            margin-bottom: 4px;
        }
        .doctor-description, .nurse-description, .assistant-info {
            font-size: 0.85em;
            color: #555;
            line-height: 1.3;
        }

        /* FAQ Section */
        .faq-list { max-width: 700px; margin: 0 auto 20px auto; }
        .faq-item {
            background-color: #fff; border: 1px solid #e0e0e0;
            border-radius: 6px; margin-bottom: 10px; overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .faq-question {
            width: 100%; background-color: transparent; border: none;
            padding: 15px; text-align: left; font-size: 1.05em; font-weight: 500;
            color: #333; cursor: pointer; display: flex; justify-content: space-between;
            align-items: center;
        }
        .faq-question::after {
            content: '\\276F'; /* Chevron right */
            font-size: 1.2em; color: #1976D2;
            transition: transform 0.3s ease;
        }
        .faq-item.active .faq-question::after { transform: rotate(90deg); }
        .faq-answer {
            padding: 0 15px; max-height: 0; overflow: hidden;
            font-size: 0.95em; color: #555; line-height: 1.6;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }
        .faq-item.active .faq-answer {
            max-height: 300px; /* Increased max-height */
            padding-bottom: 15px;
        }

        /* Provider List Section */
        #provider-list-section {
            padding: 15px;
            background-color: #fff;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        #back-to-services-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>DhipyCare - Home</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
    <style>
        /* General Body Styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            background-color: #f4f7f6; /* Light background for the main content area */
            color: #333;
            overflow-x: hidden; /* Prevent horizontal scroll issues with sidebar */
        }

        /* Sidebar Styles */
        .sidebar {
            height: 100%;
            width: 0; /* Initially hidden */
            position: fixed;
            z-index: 1001; /* Higher than sticky header */
            top: 0;
            left: 0;
            background-color: #ffffff; /* Clean white background */
            overflow-x: hidden;
            transition: 0.3s ease-in-out; /* Smooth transition for open/close */
            box-shadow: 2px 0 10px rgba(0,0,0,0.1); /* Subtle shadow */
            display: flex;
            flex-direction: column;
        }

        .sidebar.open {
            width: 280px; /* Width when open */
        }

        .sidebar-content {
            padding: 20px;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .close-sidebar {
            position: absolute;
            top: 15px;
            right: 25px;
            font-size: 36px;
            font-weight: bold;
            color: #555;
            cursor: pointer;
            transition: color 0.2s;
        }

        .close-sidebar:hover {
            color: #1a73e8;
        }

        .sidebar-content h3 { /* "Profile" heading in sidebar */
            margin-top: 40px;
            margin-bottom: 15px;
            font-size: 1.4em;
            color: #223a7a;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        #sidebar-profile-summary {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding: 10px;
            background-color: #f0f2f5;
            border-radius: 8px;
        }

        #sidebar-profile-photo-container {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #ccc; /* Placeholder background */
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden; /* Ensure images fit */
        }

        #sidebar-profile-photo-container img,
        #sidebar-profile-photo-container svg {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
         #sidebar-profile-name {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
        }

        .sidebar-content ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            flex-grow: 1;
        }

        .sidebar-content ul li {
            padding: 12px 15px;
            text-decoration: none;
            font-size: 1em;
            color: #444;
            display: block;
            transition: background-color 0.2s, color 0.2s;
            cursor: pointer;
            border-radius: 6px;
            margin-bottom: 5px;
        }

        .sidebar-content ul li:hover {
            background-color: #e9f5ff;
            color: #1a73e8;
        }

        .sidebar-content ul li#my-account-btn:before { content: "👤 "; margin-right: 8px; }
        .sidebar-content ul li#my-bookings-btn:before { content: "📅 "; margin-right: 8px; }
        .sidebar-content ul li#terms-conditions-btn:before { content: "📜 "; margin-right: 8px; }
        .sidebar-content ul li#contact-us-btn:before { content: "📞 "; margin-right: 8px; }
        .sidebar-content ul li#logout-btn:before { content: "🚪 "; margin-right: 8px; }


        .sidebar-content ul li#logout-btn {
            margin-top: auto;
            border-top: 1px solid #eee;
            padding-top: 15px;
            color: #d9534f;
        }

        .sidebar-content ul li#logout-btn:hover {
            background-color: #fbeae9;
            color: #c9302c;
        }

        #sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
            z-index: 1000;
            transition: opacity 0.3s ease-in-out;
            opacity: 0;
        }

        #sidebar-overlay.active {
            display: block;
            opacity: 1;
        }

        /* Profile Modal Styles */
        .profile-modal {
            display: none; /* Hidden by default */
            position: fixed;
            z-index: 2000; /* Above sidebar overlay */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto; /* Enable scroll if needed */
            background-color: rgba(0,0,0,0.5); /* Dim background */
            align-items: center; /* Vertical center */
            justify-content: center; /* Horizontal center */
        }

        .profile-modal-content {
            background-color: #fff;
            margin: auto; /* Responsive margin */
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            width: 90%;
            max-width: 500px; /* Max width */
            position: relative; /* For close button positioning */
            animation: fadeInModal 0.3s ease-out;
        }

        @keyframes fadeInModal {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .close-profile-modal {
            color: #aaa;
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-profile-modal:hover,
        .close-profile-modal:focus {
            color: #333;
            text-decoration: none;
        }

        .profile-modal-content h2 {
            text-align: center;
            color: #223a7a;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.6em;
        }

        #modal-profile-photo-container {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #e0e8f0;
            margin: 0 auto 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border: 3px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        #modal-profile-photo-container img,
        #modal-profile-photo-container svg {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-modal-details p {
            font-size: 1em;
            color: #444;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
        }
        .profile-modal-details p:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .profile-modal-details p strong {
            color: #223a7a;
            margin-right: 10px;
        }
        .profile-modal-details span {
            text-align: right;
            color: #555;
        }


        /* Header Styles */
        .modern-header {
            background: linear-gradient(90deg, #1976D2 0%, #3ec6e0 100%);
            color: white;
            padding: 10px 15px;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .modern-header-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        .header-profile-section {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        .profile-icon {
            /* Using an SVG for the profile icon */
            margin-right: 8px;
            width: 28px; /* Adjust size as needed */
            height: 28px; /* Adjust size as needed */
        }
        #header-profile-name {
            font-weight: 500;
            font-size: 0.95em;
        }
        .modern-location {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background-color 0.2s;
            background-color: rgba(255,255,255,0.1);
        }
        .modern-location:hover {
            background-color: rgba(255,255,255,0.2);
        }
        .location-icon {
            margin-right: 6px;
            font-size: 1.1em; /* Using text icon: 📍 */
        }
        .modern-location-text {
            font-size: 0.9em;
            font-weight: 500;
            margin-right: 4px;
        }
        .chevron {
            font-size: 0.7em; /* Using text icon: ▼ */
        }

        /* Location Modal Styles */
        .location-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.65); /* Darker overlay */
            display: none; /* Hidden by default */
            align-items: center; justify-content: center;
            z-index: 2000;
            opacity: 0; transition: opacity 0.3s ease-in-out;
        }
        .location-overlay.active {
            display: flex;
            opacity: 1;
        }
        .location-modal {
            background-color: white; padding: 30px; border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            width: 90%; max-width: 550px;
            transform: scale(0.9); transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Pop effect */
            position: relative;
        }
        .location-overlay.active .location-modal { transform: scale(1); }

        .close-location-modal {
            position: absolute; top: 12px; right: 18px; font-size: 32px; /* Larger */
            font-weight: bold; color: #888; cursor: pointer; transition: color 0.2s, transform 0.2s;
            line-height: 1; /* Ensure proper alignment */
        }
        .close-location-modal:hover { color: #333; transform: rotate(90deg); }

        .location-modal h2 {
            color: #223a7a; font-size: 1.6em; margin-top: 0; margin-bottom: 25px; text-align: center;
            font-weight: 600;
        }
        .location-modal #map {
            height: 200px; /* Fixed height for the map */
            margin-bottom: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        #selected-address {
            font-weight: 500;
            font-size: 0.95em;
            color: #333;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 6px;
            border: 1px solid #eee;
            min-height: 1.5em; /* Ensure it has some height even when empty */
            text-align: center;
        }
        .location-modal input[type="text"] {
            width: 100%; padding: 14px; margin-bottom: 18px;
            border: 1px solid #ccc; border-radius: 8px; box-sizing: border-box; font-size: 1em;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .location-modal input[type="text"]:focus {
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.15);
            outline: none;
        }
        .location-modal button {
            width: 100%; padding: 14px; border-radius: 8px; border: none;
            font-size: 1.05em; font-weight: bold; cursor: pointer; transition: background-color 0.2s, transform 0.1s;
        }
        .location-modal button:active {
            transform: scale(0.98);
        }
        #use-current-location {
            background-color: #e9f5ff; color: #1976D2; margin-bottom: 12px;
            border: 1px solid #1976D2;
        }
        #use-current-location:hover { background-color: #d4e9ff; }
        #location-submit {
            background: linear-gradient(90deg, #1976D2, #3ec6e0); color: white;
        }
        #location-submit:disabled { background: #ccc; cursor: not-allowed; }
        #location-submit:hover:not(:disabled) { opacity:0.9; }
        .location-error {
            color: #e74c3c; font-size: 0.9em; text-align: center; margin-top: 12px; min-height: 1.2em;
            font-weight: 500;
        }

        /* Main content padding */
        .main-content-area {
            padding: 15px;
            padding-top: 75px; /* Adjust if header height changes */
        }

        .section-title {
          text-align: center; color: #223a7a; margin-top:10px; margin-bottom: 20px; font-size: 1.5em; font-weight: 600;
        }

        /* Service/Horizontal Cards */
        .modern-horizontal-cards {
            display: flex;
            justify-content: space-around;
            padding: 15px 0; /* Reduced side padding as main-content-area has it */
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .modern-h-card {
            flex-basis: calc(33.333% - 20px);
            min-width: 280px;
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: flex-start; /* Align items to the top */
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            color: #333;
            border: 1px solid #e0e8f0;
        }

        .modern-h-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .modern-h-card-img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-right: 18px;
            object-fit: contain;
            flex-shrink: 0; /* Prevent image from shrinking */
        }

        .modern-h-card-content { /* New wrapper for title and features */
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .modern-h-card-title { /* Replaces modern-h-card-label */
            font-size: 1.1em;
            font-weight: bold;
            color: #223a7a;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .modern-h-card-features {
            list-style-type: disc;
            margin: 0;
            padding-left: 20px;
            font-size: 0.9em;
            color: #333;
            line-height: 1.5;
        }

        .modern-h-card-features li {
            margin-bottom: 4px;
        }

        /* .modern-h-card-label and .modern-h-card-arrow are no longer used directly, replaced by title and features */

        /* Health Issues Grid */
        .health-issues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            padding: 15px 0; /* Reduced side padding */
            margin-bottom: 20px;
        }
        .health-issue-item {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: box-shadow 0.2s, transform 0.2s, border-color 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .health-issue-item:hover {
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            border-color: #1976D2;
        }
        .health-issue-icon-placeholder {
            width: 40px;
            height: 40px;
            background-color: #e9f5ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            color: #1976D2;
            font-weight: bold;
            font-size: 0.9em;
        }
        .health-issue-item span {
            font-size: 0.95em;
            color: #333;
            font-weight: 500;
        }

        /* Scrolling Wrappers */
        .scrolling-wrapper-container {
          overflow: hidden;
          padding: 10px 0;
          margin-bottom: 15px;
          position: relative;
        }
        .scrolling-wrapper {
          display: flex;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          padding-bottom: 15px; /* space for scrollbar */
          gap: 15px;
          /* width: max-content; Remove this to allow scrollbar to appear correctly */
        }

        .scrolling-wrapper::-webkit-scrollbar {
          height: 8px;
        }
        .scrolling-wrapper::-webkit-scrollbar-track {
          background: #f0f2f5;
          border-radius: 4px;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb {
          background: #1976D2;
          border-radius: 4px;
          transition: background-color 0.2s;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb:hover {
          background: #145a9e;
        }

        .scrolling-wrapper { /* For Firefox */
          scrollbar-width: thin;
          scrollbar-color: #1976D2 #f0f2f5;
        }
        .doctor-scroll-item, .nurse-scroll-item, .assistant-scroll-item {
            flex: 0 0 auto;
            width: 150px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.07);
            padding: 12px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .doctor-scroll-item:hover, .nurse-scroll-item:hover, .assistant-scroll-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(0,0,0,0.1);
        }
        .doctor-scroll-item img, .nurse-scroll-item img, .assistant-scroll-item img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 8px;
            border: 2px solid #e0e8f0;
        }
        .doctor-name, .nurse-name, .assistant-name {
            display: block;
            font-weight: 600;
            color: #223a7a;
            font-size: 1em;
            margin-bottom: 4px;
        }
        .doctor-description, .nurse-description, .assistant-info {
            font-size: 0.85em;
            color: #555;
            line-height: 1.3;
        }

        /* FAQ Section */
        .faq-list { max-width: 700px; margin: 0 auto 20px auto; }
        .faq-item {
            background-color: #fff; border: 1px solid #e0e0e0;
            border-radius: 6px; margin-bottom: 10px; overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .faq-question {
            width: 100%; background-color: transparent; border: none;
            padding: 15px; text-align: left; font-size: 1.05em; font-weight: 500;
            color: #333; cursor: pointer; display: flex; justify-content: space-between;
            align-items: center;
        }
        .faq-question::after {
            content: '\\276F'; /* Chevron right */
            font-size: 1.2em; color: #1976D2;
            transition: transform 0.3s ease;
        }
        .faq-item.active .faq-question::after { transform: rotate(90deg); }
        .faq-answer {
            padding: 0 15px; max-height: 0; overflow: hidden;
            font-size: 0.95em; color: #555; line-height: 1.6;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }
        .faq-item.active .faq-answer {
            max-height: 300px; /* Increased max-height */
            padding-bottom: 15px;
        }

        /* Provider List Section */
        #provider-list-section {
            padding: 15px;
            background-color: #fff;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        #back-to-services-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-services-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }
        #provider-list-title {
            text-align: center;
            color: #223a7a;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        #provider-list-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        .provider-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #eee;
            box-shadow: 0 3px 7px rgba(0,0,0,0.07);
            display: flex;
            flex-direction: column;
        }
        .provider-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .provider-card-img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid #1976D2;
        }
        .provider-card-info h4 {
            margin: 0 0 4px 0;
            color: #223a7a;
            font-size: 1.1em;
        }
        .provider-card-info p {
            margin: 0;
            font-size: 0.9em;
            color: #555;
        }
        .provider-card-details {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .provider-select-btn {
            margin-top: auto; /* Pushes button to the bottom */
            padding: 10px 15px;
            border-radius: 6px;
            background-color: #1976D2;
            color: white;
            text-align: center;
            font-weight: bold;
            cursor: pointer;
            border: none;
            transition: background-color 0.2s;
        }
        .provider-select-btn:hover {
            background-color: #145a9e;
        }

        /* Patient Details Form for Health Issue */
        #health-issue-patient-form-section .form-group {
            margin-bottom: 15px;
        }
        #health-issue-patient-form-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        #health-issue-patient-form-section input[type=\"text\"],
        #health-issue-patient-form-section input[type=\"number\"],
        #health-issue-patient-form-section input[type=\"tel\"],
        #health-issue-patient-form-section select,
        #health-issue-patient-form-section textarea {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ccc;
            box-sizing: border-box;
            font-size: 1em;
        }
        #health-issue-patient-form-section textarea {
            min-height: 80px;
        }
        #health-issue-patient-form-section button[type=\"submit\"] {
            width: 100%;
            padding: 14px;
            border-radius: 8px;
            border: none;
            background: linear-gradient(90deg, #1976D2, #3ec6e0);
            color: white;
            font-size: 1.05em;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        #health-issue-patient-form-section button[type=\"submit\"]:hover {
            opacity: 0.9;
        }
        #back-to-overview-from-patient-form-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-overview-from-patient-form-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }

        /* Responsive Styles */
        @media screen and (max-width: 768px) {
            .sidebar.open {
                width: 250px;
            }
            .sidebar-content h3 {
                font-size: 1.3em;
            }
            #sidebar-profile-name {
                font-size: 1em;
            }
            .sidebar-content ul li {
                font-size: 0.95em;
                padding: 10px 12px;
            }

            .main-content-area {
                padding: 10px;
                padding-top: 65px; /* Adjust if header height changes */
            }
            .modern-header { padding: 8px 10px; }
            .header-profile-section { flex-shrink: 0; }
            #header-profile-name {
                font-size: 0.9em;
                max-width: 100px; /* Ensure name doesn't push content */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .modern-location { padding: 6px 8px; }
            .modern-location-text { font-size: 0.85em; }
            .location-icon { font-size: 1em; }
            .chevron { font-size: 0.6em; }

            .modern-horizontal-cards { flex-direction: column; padding: 10px 0; gap:10px; }
            .modern-h-card { min-width: unset; width:100%; flex-basis: auto; }
            .modern-h-card-title { font-size: 1em; }

            .section-title { font-size: 1.3em; margin-bottom: 15px; }
            .health-issues-grid { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; padding:10px 0; }
            .health-issue-item { padding: 10px; }
            .health-issue-icon-placeholder { width: 35px; height: 35px; margin-bottom: 8px; }
            .health-issue-item span { font-size: 0.9em; }

            .profile-modal-content { width: 95%; padding: 20px; }
            .profile-modal-content h2 { font-size: 1.4em; }
            #modal-profile-photo-container { width: 100px; height: 100px; }
            .profile-modal-details p { font-size: 0.95em; }

            .location-modal { width: 95%; padding: 20px; }
            .location-modal h2 { font-size: 1.4em; }
            .location-modal #map { height: 180px; }
        }

        /* Fallback for profile icon if SVG fails or for general use */
        .default-profile-icon-fallback {
            display: inline-block;
            width: 24px;
            height: 24px;
            background-color: #fff; /* White background */
            border-radius: 50%;
            text-align: center;
            line-height: 24px; /* Vertically center */
            font-weight: bold;
            color: #1976D2; /* Blue initial */
            font-size: 14px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .main-content-area {
                padding: 15px;
            }

            .modern-horizontal-cards, /* Keep for provider list if it uses this class */
            .modern-vertical-service-cards {
                grid-template-columns: 1fr; /* Fallback for vertical stack */
                display: flex; /* For vertical stack */
                flex-direction: column; /* For vertical stack */
            }

            .health-issues-grid {
                grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
                gap: 15px;
            }

            .modern-h-card,
            .health-issue-item,
            .patient-form {
                padding: 20px;
            }

            .modern-h-card-img {
                width: 50px;
                height: 50px;
            }

            .modern-h-card-title {
                font-size: 18px;
            }

            /* Quick Actions Responsive */
            #quick-actions-container {
                flex-direction: column;
                gap: 10px;
            }
            .quick-action-card {
                padding: 15px;
            }
            .quick-action-icon {
                width: 35px; height: 35px; margin-bottom: 8px;
            }
            .quick-action-text {
                font-size: 1em;
            }
        }

        /* NEW STYLES FOR QUICK ACTIONS AND REVISED LAYOUT */
        #quick-actions-container {
            display: flex;
            flex-direction: column; /* Stack quick actions vertically */
            gap: 10px; /* Space between quick action cards */
            margin-bottom: 30px;
        }

        .quick-action-card {
            background-color: #fff;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: box-shadow 0.2s ease;
            border: 1px solid #e8e8e8;
        }

        .quick-action-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }

        .quick-action-icon {
            width: 40px; /* Adjust as needed */
            height: 40px; /* Adjust as needed */
            margin-right: 15px;
            object-fit: contain;
        }

        .quick-action-text {
            flex-grow: 1;
            font-size: 1.1em;
            font-weight: 500;
            color: #333;
        }

        .quick-action-arrow {
            font-size: 1.2em;
            color: #888;
        }

        /* Styling for Detailed Service Cards (Vertical Stack) */
        .modern-vertical-service-cards {
            display: flex;
            flex-direction: column;
            gap: 15px; /* Space between detailed service cards */
            margin-bottom: 30px;
        }

        /* .modern-h-card styles (used for detailed cards) should already be mostly fine,
           but ensure they work well in a vertical stack.
           The existing .modern-h-card styles will be used. */

        .section-title {
            /* Ensure section titles are styled as per screenshot (prominent, blue-ish) */
            color: #1e3a8a; /* Example blue color */
            font-weight: 600;
            font-size: 1.6em; /* Make it larger */
            margin-top: 30px;
            margin-bottom: 20px;
            text-align: left; /* As per screenshot */
        }

        /* FAQ Styling Update */
        .faq-item .faq-question::after {
            content: '+'; /* Change chevron to plus */
            font-size: 1.5em; /* Make plus icon larger */
            color: #1e3a8a; /* Match title color */
        }

        .faq-item.active .faq-question::after {
            content: '−'; /* Change to minus when active */
            transform: none; /* Remove rotation */
        }

        /* NEW HEADER STYLES */
        .new-main-header {
            background-color: #1e3a8a; /* Dark blue background */
            color: white;
            padding: 10px 15px;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .user-info-line {
            display: flex;
            align-items: center;
            margin-bottom: 8px; /* Space between user line and location line */
        }
        .user-avatar-icon {
            width: 32px; /* Adjust size */
            height: 32px; /* Adjust size */
            margin-right: 10px;
            cursor: pointer;
            border-radius: 50%; /* If the SVG itself isn't round */
        }
        .user-name-display {
            font-weight: 500;
            font-size: 1.1em;
        }
        .location-line {
            display: flex;
            align-items: center;
            cursor: pointer;
            background-color: white; /* White background for location bar */
            color: #333; /* Dark text for location */
            padding: 8px 12px;
            border-radius: 8px;
        }
        .location-pin-icon {
            margin-right: 8px;
            font-size: 1.1em;
        }
        .location-address-text {
            font-size: 0.95em;
            font-weight: 500;
            margin-right: auto; /* Pushes chevron to the right */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px; /* Adjust as needed */
        }
        .location-chevron-icon {
            font-size: 0.8em;
        }
        /* END NEW HEADER STYLES */

        /* Location Modal Styles (ensure they work with new header trigger) */
        .location-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.65); /* Darker overlay */
            display: none; /* Hidden by default */
            align-items: center; justify-content: center;
            z-index: 2000;
            opacity: 0; transition: opacity 0.3s ease-in-out;
        }
        .location-overlay.active {
            display: flex;
            opacity: 1;
        }
        .location-modal {
            background-color: white; padding: 30px; border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            width: 90%; max-width: 550px;
            transform: scale(0.9); transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Pop effect */
            position: relative;
        }
        .location-overlay.active .location-modal { transform: scale(1); }

        .close-location-modal {
            position: absolute; top: 12px; right: 18px; font-size: 32px; /* Larger */
            font-weight: bold; color: #888; cursor: pointer; transition: color 0.2s, transform 0.2s;
            line-height: 1; /* Ensure proper alignment */
        }
        .close-location-modal:hover { color: #333; transform: rotate(90deg); }

        .location-modal h2 {
            color: #223a7a; font-size: 1.6em; margin-top: 0; margin-bottom: 25px; text-align: center;
            font-weight: 600;
        }
        .location-modal #map {
            height: 200px; /* Fixed height for the map */
            margin-bottom: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        #selected-address {
            font-weight: 500;
            font-size: 0.95em;
            color: #333;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 6px;
            border: 1px solid #eee;
            min-height: 1.5em; /* Ensure it has some height even when empty */
            text-align: center;
        }
        .location-modal input[type="text"] {
            width: 100%; padding: 14px; margin-bottom: 18px;
            border: 1px solid #ccc; border-radius: 8px; box-sizing: border-box; font-size: 1em;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .location-modal input[type="text"]:focus {
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.15);
            outline: none;
        }
        .location-modal button {
            width: 100%; padding: 14px; border-radius: 8px; border: none;
            font-size: 1.05em; font-weight: bold; cursor: pointer; transition: background-color 0.2s, transform 0.1s;
        }
        .location-modal button:active {
            transform: scale(0.98);
        }
        #use-current-location {
            background-color: #e9f5ff; color: #1976D2; margin-bottom: 12px;
            border: 1px solid #1976D2;
        }
        #use-current-location:hover { background-color: #d4e9ff; }
        #location-submit {
            background: linear-gradient(90deg, #1976D2, #3ec6e0); color: white;
        }
        #location-submit:disabled { background: #ccc; cursor: not-allowed; }
        #location-submit:hover:not(:disabled) { opacity:0.9; }
        .location-error {
            color: #e74c3c; font-size: 0.9em; text-align: center; margin-top: 12px; min-height: 1.2em;
            font-weight: 500;
        }

        /* Main content padding */
        .main-content-area {
            padding: 15px;
            padding-top: 75px; /* Adjust if header height changes */
        }

        .section-title {
          text-align: center; color: #223a7a; margin-top:10px; margin-bottom: 20px; font-size: 1.5em; font-weight: 600;
        }

        /* Service/Horizontal Cards */
        .modern-horizontal-cards {
            display: flex;
            justify-content: space-around;
            padding: 15px 0; /* Reduced side padding as main-content-area has it */
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .modern-h-card {
            flex-basis: calc(33.333% - 20px);
            min-width: 280px;
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: flex-start; /* Align items to the top */
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            color: #333;
            border: 1px solid #e0e8f0;
        }

        .modern-h-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .modern-h-card-img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-right: 18px;
            object-fit: contain;
            flex-shrink: 0; /* Prevent image from shrinking */
        }

        .modern-h-card-content { /* New wrapper for title and features */
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .modern-h-card-title { /* Replaces modern-h-card-label */
            font-size: 1.1em;
            font-weight: bold;
            color: #223a7a;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .modern-h-card-features {
            list-style-type: disc;
            margin: 0;
            padding-left: 20px;
            font-size: 0.9em;
            color: #333;
            line-height: 1.5;
        }

        .modern-h-card-features li {
            margin-bottom: 4px;
        }

        /* .modern-h-card-label and .modern-h-card-arrow are no longer used directly, replaced by title and features */

        /* Health Issues Grid */
        .health-issues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            padding: 15px 0; /* Reduced side padding */
            margin-bottom: 20px;
        }
        .health-issue-item {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: box-shadow 0.2s, transform 0.2s, border-color 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .health-issue-item:hover {
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            border-color: #1976D2;
        }
        .health-issue-icon-placeholder {
            width: 40px;
            height: 40px;
            background-color: #e9f5ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            color: #1976D2;
            font-weight: bold;
            font-size: 0.9em;
        }
        .health-issue-item span {
            font-size: 0.95em;
            color: #333;
            font-weight: 500;
        }

        /* Scrolling Wrappers */
        .scrolling-wrapper-container {
          overflow: hidden;
          padding: 10px 0;
          margin-bottom: 15px;
          position: relative;
        }
        .scrolling-wrapper {
          display: flex;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          padding-bottom: 15px; /* space for scrollbar */
          gap: 15px;
          /* width: max-content; Remove this to allow scrollbar to appear correctly */
        }

        .scrolling-wrapper::-webkit-scrollbar {
          height: 8px;
        }
        .scrolling-wrapper::-webkit-scrollbar-track {
          background: #f0f2f5;
          border-radius: 4px;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb {
          background: #1976D2;
          border-radius: 4px;
          transition: background-color 0.2s;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb:hover {
          background: #145a9e;
        }

        .scrolling-wrapper { /* For Firefox */
          scrollbar-width: thin;
          scrollbar-color: #1976D2 #f0f2f5;
        }
        .doctor-scroll-item, .nurse-scroll-item, .assistant-scroll-item {
            flex: 0 0 auto;
            width: 150px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.07);
            padding: 12px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .doctor-scroll-item:hover, .nurse-scroll-item:hover, .assistant-scroll-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(0,0,0,0.1);
        }
        .doctor-scroll-item img, .nurse-scroll-item img, .assistant-scroll-item img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 8px;
            border: 2px solid #e0e8f0;
        }
        .doctor-name, .nurse-name, .assistant-name {
            display: block;
            font-weight: 600;
            color: #223a7a;
            font-size: 1em;
            margin-bottom: 4px;
        }
        .doctor-description, .nurse-description, .assistant-info {
            font-size: 0.85em;
            color: #555;
            line-height: 1.3;
        }

        /* FAQ Section */
        .faq-list { max-width: 700px; margin: 0 auto 20px auto; }
        .faq-item {
            background-color: #fff; border: 1px solid #e0e0e0;
            border-radius: 6px; margin-bottom: 10px; overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .faq-question {
            width: 100%; background-color: transparent; border: none;
            padding: 15px; text-align: left; font-size: 1.05em; font-weight: 500;
            color: #333; cursor: pointer; display: flex; justify-content: space-between;
            align-items: center;
        }
        .faq-question::after {
            content: '\\276F'; /* Chevron right */
            font-size: 1.2em; color: #1976D2;
            transition: transform 0.3s ease;
        }
        .faq-item.active .faq-question::after { transform: rotate(90deg); }
        .faq-answer {
            padding: 0 15px; max-height: 0; overflow: hidden;
            font-size: 0.95em; color: #555; line-height: 1.6;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }
        .faq-item.active .faq-answer {
            max-height: 300px; /* Increased max-height */
            padding-bottom: 15px;
        }

        /* Provider List Section */
        #provider-list-section {
            padding: 15px;
            background-color: #fff;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        #back-to-services-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-services-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }
        #provider-list-title {
            text-align: center;
            color: #223a7a;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        #provider-list-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        .provider-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #eee;
            box-shadow: 0 3px 7px rgba(0,0,0,0.07);
            display: flex;
            flex-direction: column;
        }
        .provider-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .provider-card-img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid #1976D2;
        }
        .provider-card-info h4 {
            margin: 0 0 4px 0;
            color: #223a7a;
            font-size: 1.1em;
        }
        .provider-card-info p {
            margin: 0;
            font-size: 0.9em;
            color: #555;
        }
        .provider-card-details {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .provider-select-btn {
            margin-top: auto; /* Pushes button to the bottom */
            padding: 10px 15px;
            border-radius: 6px;
            background-color: #1976D2;
            color: white;
            text-align: center;
            font-weight: bold;
            cursor: pointer;
            border: none;
            transition: background-color 0.2s;
        }
        .provider-select-btn:hover {
            background-color: #145a9e;
        }

        /* Patient Details Form for Health Issue */
        #health-issue-patient-form-section .form-group {
            margin-bottom: 15px;
        }
        #health-issue-patient-form-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        #health-issue-patient-form-section input[type=\"text\"],
        #health-issue-patient-form-section input[type=\"number\"],
        #health-issue-patient-form-section input[type=\"tel\"],
        #health-issue-patient-form-section select,
        #health-issue-patient-form-section textarea {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ccc;
            box-sizing: border-box;
            font-size: 1em;
        }
        #health-issue-patient-form-section textarea {
            min-height: 80px;
        }
        #health-issue-patient-form-section button[type=\"submit\"] {
            width: 100%;
            padding: 14px;
            border-radius: 8px;
            border: none;
            background: linear-gradient(90deg, #1976D2, #3ec6e0);
            color: white;
            font-size: 1.05em;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        #health-issue-patient-form-section button[type=\"submit\"]:hover {
            opacity: 0.9;
        }
        #back-to-overview-from-patient-form-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-overview-from-patient-form-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }

        /* Responsive Styles */
        @media screen and (max-width: 768px) {
            .sidebar.open {
                width: 250px;
            }
            .sidebar-content h3 {
                font-size: 1.3em;
            }
            #sidebar-profile-name {
                font-size: 1em;
            }
            .sidebar-content ul li {
                font-size: 0.95em;
                padding: 10px 12px;
            }

            .main-content-area {
                padding: 10px;
                padding-top: 65px; /* Adjust if header height changes */
            }
            .modern-header { padding: 8px 10px; }
            .header-profile-section { flex-shrink: 0; }
            #header-profile-name {
                font-size: 0.9em;
                max-width: 100px; /* Ensure name doesn't push content */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .modern-location { padding: 6px 8px; }
            .modern-location-text { font-size: 0.85em; }
            .location-icon { font-size: 1em; }
            .chevron { font-size: 0.6em; }

            .modern-horizontal-cards { flex-direction: column; padding: 10px 0; gap:10px; }
            .modern-h-card { min-width: unset; width:100%; flex-basis: auto; }
            .modern-h-card-title { font-size: 1em; }

            .section-title { font-size: 1.3em; margin-bottom: 15px; }
            .health-issues-grid { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; padding:10px 0; }
            .health-issue-item { padding: 10px; }
            .health-issue-icon-placeholder { width: 35px; height: 35px; margin-bottom: 8px; }
            .health-issue-item span { font-size: 0.9em; }

            .profile-modal-content { width: 95%; padding: 20px; }
            .profile-modal-content h2 { font-size: 1.4em; }
            #modal-profile-photo-container { width: 100px; height: 100px; }
            .profile-modal-details p { font-size: 0.95em; }

            .location-modal { width: 95%; padding: 20px; }
            .location-modal h2 { font-size: 1.4em; }
            .location-modal #map { height: 180px; }
        }

        /* Fallback for profile icon if SVG fails or for general use */
        .default-profile-icon-fallback {
            display: inline-block;
            width: 24px;
            height: 24px;
            background-color: #fff; /* White background */
            border-radius: 50%;
            text-align: center;
            line-height: 24px; /* Vertically center */
            font-weight: bold;
            color: #1976D2; /* Blue initial */
            font-size: 14px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .main-content-area {
                padding: 15px;
            }

            .modern-horizontal-cards, /* Keep for provider list if it uses this class */
            .modern-vertical-service-cards {
                grid-template-columns: 1fr; /* Fallback for vertical stack */
                display: flex; /* For vertical stack */
                flex-direction: column; /* For vertical stack */
            }

            .health-issues-grid {
                grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
                gap: 15px;
            }

            .modern-h-card,
            .health-issue-item,
            .patient-form {
                padding: 20px;
            }

            .modern-h-card-img {
                width: 50px;
                height: 50px;
            }

            .modern-h-card-title {
                font-size: 18px;
            }

            /* Quick Actions Responsive */
            #quick-actions-container {
                flex-direction: column;
                gap: 10px;
            }
            .quick-action-card {
                padding: 15px;
            }
            .quick-action-icon {
                width: 35px; height: 35px; margin-bottom: 8px;
            }
            .quick-action-text {
                font-size: 1em;
            }
        }

        /* NEW STYLES FOR QUICK ACTIONS AND REVISED LAYOUT */
        #quick-actions-container {
            display: flex;
            flex-direction: column; /* Stack quick actions vertically */
            gap: 10px; /* Space between quick action cards */
            margin-bottom: 30px;
        }

        .quick-action-card {
            background-color: #fff;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: box-shadow 0.2s ease;
            border: 1px solid #e8e8e8;
        }

        .quick-action-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }

        .quick-action-icon {
            width: 40px; /* Adjust as needed */
            height: 40px; /* Adjust as needed */
            margin-right: 15px;
            object-fit: contain;
        }

        .quick-action-text {
            flex-grow: 1;
            font-size: 1.1em;
            font-weight: 500;
            color: #333;
        }

        .quick-action-arrow {
            font-size: 1.2em;
            color: #888;
        }

        /* Styling for Detailed Service Cards (Vertical Stack) */
        .modern-vertical-service-cards {
            display: flex;
            flex-direction: column;
            gap: 15px; /* Space between detailed service cards */
            margin-bottom: 30px;
        }

        /* .modern-h-card styles (used for detailed cards) should already be mostly fine,
           but ensure they work well in a vertical stack.
           The existing .modern-h-card styles will be used. */

        .section-title {
            /* Ensure section titles are styled as per screenshot (prominent, blue-ish) */
            color: #1e3a8a; /* Example blue color */
            font-weight: 600;
            font-size: 1.6em; /* Make it larger */
            margin-top: 30px;
            margin-bottom: 20px;
            text-align: left; /* As per screenshot */
        }

        /* FAQ Styling Update */
        .faq-item .faq-question::after {
            content: '+'; /* Change chevron to plus */
            font-size: 1.5em; /* Make plus icon larger */
            color: #1e3a8a; /* Match title color */
        }

        .faq-item.active .faq-question::after {
            content: '−'; /* Change to minus when active */
            transform: none; /* Remove rotation */
        }

        /* NEW HEADER STYLES */
        .new-main-header {
            background-color: #1e3a8a; /* Dark blue background */
            color: white;
            padding: 10px 15px;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .user-info-line {
            display: flex;
            align-items: center;
            margin-bottom: 8px; /* Space between user line and location line */
        }
        .user-avatar-icon {
            width: 32px; /* Adjust size */
            height: 32px; /* Adjust size */
            margin-right: 10px;
            cursor: pointer;
            border-radius: 50%; /* If the SVG itself isn't round */
        }
        .user-name-display {
            font-weight: 500;
            font-size: 1.1em;
        }
        .location-line {
            display: flex;
            align-items: center;
            cursor: pointer;
            background-color: white; /* White background for location bar */
            color: #333; /* Dark text for location */
            padding: 8px 12px;
            border-radius: 8px;
        }
        .location-pin-icon {
            margin-right: 8px;
            font-size: 1.1em;
        }
        .location-address-text {
            font-size: 0.95em;
            font-weight: 500;
            margin-right: auto; /* Pushes chevron to the right */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px; /* Adjust as needed */
        }
        .location-chevron-icon {
            font-size: 0.8em;
        }
        /* END NEW HEADER STYLES */

        /* Location Modal Styles (ensure they work with new header trigger) */
        .location-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.65); /* Darker overlay */
            display: none; /* Hidden by default */
            align-items: center; justify-content: center;
            z-index: 2000;
            opacity: 0; transition: opacity 0.3s ease-in-out;
        }
        .location-overlay.active {
            display: flex;
            opacity: 1;
        }
        .location-modal {
            background-color: white; padding: 30px; border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            width: 90%; max-width: 550px;
            transform: scale(0.9); transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Pop effect */
            position: relative;
        }
        .location-overlay.active .location-modal { transform: scale(1); }

        .close-location-modal {
            position: absolute; top: 12px; right: 18px; font-size: 32px; /* Larger */
            font-weight: bold; color: #888; cursor: pointer; transition: color 0.2s, transform 0.2s;
            line-height: 1; /* Ensure proper alignment */
        }
        .close-location-modal:hover { color: #333; transform: rotate(90deg); }

        .location-modal h2 {
            color: #223a7a; font-size: 1.6em; margin-top: 0; margin-bottom: 25px; text-align: center;
            font-weight: 600;
        }
        .location-modal #map {
            height: 200px; /* Fixed height for the map */
            margin-bottom: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        #selected-address {
            font-weight: 500;
            font-size: 0.95em;
            color: #333;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 6px;
            border: 1px solid #eee;
            min-height: 1.5em; /* Ensure it has some height even when empty */
            text-align: center;
        }
        .location-modal input[type="text"] {
            width: 100%; padding: 14px; margin-bottom: 18px;
            border: 1px solid #ccc; border-radius: 8px; box-sizing: border-box; font-size: 1em;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .location-modal input[type="text"]:focus {
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.15);
            outline: none;
        }
        .location-modal button {
            width: 100%; padding: 14px; border-radius: 8px; border: none;
            font-size: 1.05em; font-weight: bold; cursor: pointer; transition: background-color 0.2s, transform 0.1s;
        }
        .location-modal button:active {
            transform: scale(0.98);
        }
        #use-current-location {
            background-color: #e9f5ff; color: #1976D2; margin-bottom: 12px;
            border: 1px solid #1976D2;
        }
        #use-current-location:hover { background-color: #d4e9ff; }
        #location-submit {
            background: linear-gradient(90deg, #1976D2, #3ec6e0); color: white;
        }
        #location-submit:disabled { background: #ccc; cursor: not-allowed; }
        #location-submit:hover:not(:disabled) { opacity:0.9; }
        .location-error {
            color: #e74c3c; font-size: 0.9em; text-align: center; margin-top: 12px; min-height: 1.2em;
            font-weight: 500;
        }

        /* Main content padding */
        .main-content-area {
            padding: 15px;
            padding-top: 75px; /* Adjust if header height changes */
        }

        .section-title {
          text-align: center; color: #223a7a; margin-top:10px; margin-bottom: 20px; font-size: 1.5em; font-weight: 600;
        }

        /* Service/Horizontal Cards */
        .modern-horizontal-cards {
            display: flex;
            justify-content: space-around;
            padding: 15px 0; /* Reduced side padding as main-content-area has it */
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .modern-h-card {
            flex-basis: calc(33.333% - 20px);
            min-width: 280px;
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: flex-start; /* Align items to the top */
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            color: #333;
            border: 1px solid #e0e8f0;
        }

        .modern-h-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .modern-h-card-img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-right: 18px;
            object-fit: contain;
            flex-shrink: 0; /* Prevent image from shrinking */
        }

        .modern-h-card-content { /* New wrapper for title and features */
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .modern-h-card-title { /* Replaces modern-h-card-label */
            font-size: 1.1em;
            font-weight: bold;
            color: #223a7a;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .modern-h-card-features {
            list-style-type: disc;
            margin: 0;
            padding-left: 20px;
            font-size: 0.9em;
            color: #333;
            line-height: 1.5;
        }

        .modern-h-card-features li {
            margin-bottom: 4px;
        }

        /* .modern-h-card-label and .modern-h-card-arrow are no longer used directly, replaced by title and features */

        /* Health Issues Grid */
        .health-issues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            padding: 15px 0; /* Reduced side padding */
            margin-bottom: 20px;
        }
        .health-issue-item {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: box-shadow 0.2s, transform 0.2s, border-color 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .health-issue-item:hover {
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            border-color: #1976D2;
        }
        .health-issue-icon-placeholder {
            width: 40px;
            height: 40px;
            background-color: #e9f5ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            color: #1976D2;
            font-weight: bold;
            font-size: 0.9em;
        }
        .health-issue-item span {
            font-size: 0.95em;
            color: #333;
            font-weight: 500;
        }

        /* Scrolling Wrappers */
        .scrolling-wrapper-container {
          overflow: hidden;
          padding: 10px 0;
          margin-bottom: 15px;
          position: relative;
        }
        .scrolling-wrapper {
          display: flex;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          padding-bottom: 15px; /* space for scrollbar */
          gap: 15px;
          /* width: max-content; Remove this to allow scrollbar to appear correctly */
        }

        .scrolling-wrapper::-webkit-scrollbar {
          height: 8px;
        }
        .scrolling-wrapper::-webkit-scrollbar-track {
          background: #f0f2f5;
          border-radius: 4px;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb {
          background: #1976D2;
          border-radius: 4px;
          transition: background-color 0.2s;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb:hover {
          background: #145a9e;
        }

        .scrolling-wrapper { /* For Firefox */
          scrollbar-width: thin;
          scrollbar-color: #1976D2 #f0f2f5;
        }
        .doctor-scroll-item, .nurse-scroll-item, .assistant-scroll-item {
            flex: 0 0 auto;
            width: 150px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.07);
            padding: 12px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .doctor-scroll-item:hover, .nurse-scroll-item:hover, .assistant-scroll-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(0,0,0,0.1);
        }
        .doctor-scroll-item img, .nurse-scroll-item img, .assistant-scroll-item img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 8px;
            border: 2px solid #e0e8f0;
        }
        .doctor-name, .nurse-name, .assistant-name {
            display: block;
            font-weight: 600;
            color: #223a7a;
            font-size: 1em;
            margin-bottom: 4px;
        }
        .doctor-description, .nurse-description, .assistant-info {
            font-size: 0.85em;
            color: #555;
            line-height: 1.3;
        }

        /* FAQ Section */
        .faq-list { max-width: 700px; margin: 0 auto 20px auto; }
        .faq-item {
            background-color: #fff; border: 1px solid #e0e0e0;
            border-radius: 6px; margin-bottom: 10px; overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .faq-question {
            width: 100%; background-color: transparent; border: none;
            padding: 15px; text-align: left; font-size: 1.05em; font-weight: 500;
            color: #333; cursor: pointer; display: flex; justify-content: space-between;
            align-items: center;
        }
        .faq-question::after {
            content: '\\276F'; /* Chevron right */
            font-size: 1.2em; color: #1976D2;
            transition: transform 0.3s ease;
        }
        .faq-item.active .faq-question::after { transform: rotate(90deg); }
        .faq-answer {
            padding: 0 15px; max-height: 0; overflow: hidden;
            font-size: 0.95em; color: #555; line-height: 1.6;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }
        .faq-item.active .faq-answer {
            max-height: 300px; /* Increased max-height */
            padding-bottom: 15px;
        }

        /* Provider List Section */
        #provider-list-section {
            padding: 15px;
            background-color: #fff;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        #back-to-services-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-services-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }
        #provider-list-title {
            text-align: center;
            color: #223a7a;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        #provider-list-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        .provider-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #eee;
            box-shadow: 0 3px 7px rgba(0,0,0,0.07);
            display: flex;
            flex-direction: column;
        }
        .provider-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .provider-card-img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid #1976D2;
        }
        .provider-card-info h4 {
            margin: 0 0 4px 0;
            color: #223a7a;
            font-size: 1.1em;
        }
        .provider-card-info p {
            margin: 0;
            font-size: 0.9em;
            color: #555;
        }
        .provider-card-details {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .provider-select-btn {
            margin-top: auto; /* Pushes button to the bottom */
            padding: 10px 15px;
            border-radius: 6px;
            background-color: #1976D2;
            color: white;
            text-align: center;
            font-weight: bold;
            cursor: pointer;
            border: none;
            transition: background-color 0.2s;
        }
        .provider-select-btn:hover {
            background-color: #145a9e;
        }

        /* Patient Details Form for Health Issue */
        #health-issue-patient-form-section .form-group {
            margin-bottom: 15px;
        }
        #health-issue-patient-form-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        #health-issue-patient-form-section input[type=\"text\"],
        #health-issue-patient-form-section input[type=\"number\"],
        #health-issue-patient-form-section input[type=\"tel\"],
        #health-issue-patient-form-section select,
        #health-issue-patient-form-section textarea {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ccc;
            box-sizing: border-box;
            font-size: 1em;
        }
        #health-issue-patient-form-section textarea {
            min-height: 80px;
        }
        #health-issue-patient-form-section button[type=\"submit\"] {
            width: 100%;
            padding: 14px;
            border-radius: 8px;
            border: none;
            background: linear-gradient(90deg, #1976D2, #3ec6e0);
            color: white;
            font-size: 1.05em;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        #health-issue-patient-form-section button[type=\"submit\"]:hover {
            opacity: 0.9;
        }
        #back-to-overview-from-patient-form-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 18px;
            border-radius: 20px;
            border: 1px solid #1976D2;
            background-color: #fff;
            color: #1976D2;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        #back-to-overview-from-patient-form-btn:hover {
            background-color: #e9f5ff;
            color: #145a9e;
        }

        /* Responsive Styles */
        @media screen and (max-width: 768px) {
            .sidebar.open {
                width: 250px;
            }
            .sidebar-content h3 {
                font-size: 1.3em;
            }
            #sidebar-profile-name {
                font-size: 1em;
            }
            .sidebar-content ul li {
                font-size: 0.95em;
                padding: 10px 12px;
            }

            .main-content-area {
                padding: 10px;
                padding-top: 65px; /* Adjust if header height changes */
            }
            .modern-header { padding: 8px 10px; }
            .header-profile-section { flex-shrink: 0; }
            #header-profile-name {
                font-size: 0.9em;
                max-width: 100px; /* Ensure name doesn't push content */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .modern-location { padding: 6px 8px; }
            .modern-location-text { font-size: 0.85em; }
            .location-icon { font-size: 1em; }
            .chevron { font-size: 0.6em; }

            .modern-horizontal-cards { flex-direction: column; padding: 10px 0; gap:10px; }
            .modern-h-card { min-width: unset; width:100%; flex-basis: auto; }
            .modern-h-card-title { font-size: 1em; }

            .section-title { font-size: 1.3em; margin-bottom: 15px; }
            .health-issues-grid { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; padding:10px 0; }
            .health-issue-item { padding: 10px; }
            .health-issue-icon-placeholder { width: 35px; height: 35px; margin-bottom: 8px; }
            .health-issue-item span { font-size: 0.9em; }

            .profile-modal-content { width: 95%; padding: 20px; }
            .profile-modal-content h2 { font-size: 1.4em; }
            #modal-profile-photo-container { width: 100px; height: 100px; }
            .profile-modal-details p { font-size: 0.95em; }

            .location-modal { width: 95%; padding: 20px; }
            .location-modal h2 { font-size: 1.4em; }
            .location-modal #map { height: 180px; }
        }

        /* Fallback for profile icon if SVG fails or for general use */
        .default-profile-icon-fallback {
            display: inline-block;
            width: 24px;
            height: 24px;
            background-color: #fff; /* White background */
            border-radius: 50%;
            text-align: center;
            line-height: 24px; /* Vertically center */
            font-weight: bold;
            color: #1976D2; /* Blue initial */
            font-size: 14px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .main-content-area {
                padding: 15px;
            }

            .modern-horizontal-cards, /* Keep for provider list if it uses this class */
            .modern-vertical-service-cards {
                grid-template-columns: 1fr; /* Fallback for vertical stack */
                display: flex; /* For vertical stack */
                flex-direction: column; /* For vertical stack */
            }

            .health-issues-grid {
                grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
                gap: 15px;
            }

            .modern-h-card,
            .health-issue-item,
            .patient-form {
                padding: 20px;
            }

            .modern-h-card-img {
                width: 50px;
                height: 50px;
            }

            .modern-h-card-title {
                font-size: 18px;
            }

            /* Quick Actions Responsive */
            #quick-actions-container {
                flex-direction: column;
                gap: 10px;
            }
            .quick-action-card {
                padding: 15px;
            }
            .quick-action-icon {
                width: 35px; height: 35px; margin-bottom: 8px;
            }
            .quick-action-text {
                font-size: 1em;
            }
        }

        /* NEW STYLES FOR QUICK ACTIONS AND REVISED LAYOUT */
        #quick-actions-container {
            display: flex;
            flex-direction: column; /* Stack quick actions vertically */
            gap: 10px; /* Space between quick action cards */
            margin-bottom: 30px;
        }

        .quick-action-card {
            background-color: #fff;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: box-shadow 0.2s ease;
            border: 1px solid #e8e8e8;
        }

        .quick-action-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }

        .quick-action-icon {
            width: 40px; /* Adjust as needed */
            height: 40px; /* Adjust as needed */
            margin-right: 15px;
            object-fit: contain;
        }

        .quick-action-text {
            flex-grow: 1;
            font-size: 1.1em;
            font-weight: 500;
            color: #333;
        }

        .quick-action-arrow {
            font-size: 1.2em;
            color: #888;
        }

        /* Styling for Detailed Service Cards (Vertical Stack) */
        .modern-vertical-service-cards {
            display: flex;
            flex-direction: column;
            gap: 15px; /* Space between detailed service cards */
            margin-bottom: 30px;
        }

        /* .modern-h-card styles (used for detailed cards) should already be mostly fine,
           but ensure they work well in a vertical stack.
           The existing .modern-h-card styles will be used. */

        .section-title {
            /* Ensure section titles are styled as per screenshot (prominent, blue-ish) */
            color: #1e3a8a; /* Example blue color */
            font-weight: 600;
            font-size: 1.6em; /* Make it larger */
            margin-top: 30px;
            margin-bottom: 20px;
            text-align: left; /* As per screenshot */
        }

        /* FAQ Styling Update */
        .faq-item .faq-question::after {
            content: '+'; /* Change chevron to plus */
            font-size: 1.5em; /* Make plus icon larger */
            color: #1e3a8a; /* Match title color */
        }

        .faq-item.active .faq-question::after {
            content: '−'; /* Change to minus when active */
            transform: none; /* Remove rotation */
        }

    </style>
</head>
<body>

    <!-- Sidebar -->
    <div id="sidebar" class="sidebar">
        <div class="sidebar-content">
            <span class="close-sidebar" id="close-sidebar-btn">&times;</span>
            <h3>Profile</h3>
            <div id="sidebar-profile-summary">
                <div id="sidebar-profile-photo-container">
                    <!-- SVG Placeholder for Profile Photo -->
                    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="50" fill="#ddd"/><path d="M50 62.5C40.075 62.5 31.25 68.125 31.25 75V81.25H68.75V75C68.75 68.125 59.925 62.5 50 62.5ZM50 25C55.175 25 59.375 29.2 59.375 34.375C59.375 39.55 55.175 43.75 50 43.75C44.825 43.75 40.625 39.55 40.625 34.375C40.625 29.2 44.825 25 50 25Z" fill="#aaa"/></svg>
                </div>
                <span id="sidebar-profile-name">User Name</span>
            </div>
            <ul>
                <li id="my-account-btn">My Account</li>
                <li id="my-bookings-btn">My Bookings</li>
                <li id="terms-conditions-btn">Terms & Conditions</li>
                <li id="contact-us-btn">Contact Us</li>
                <li id="logout-btn">Logout</li>
            </ul>
        </div>
    </div>
    <div id="sidebar-overlay"></div>

    <!-- Profile Modal -->
    <div id="profile-modal" class="profile-modal">
        <div class="profile-modal-content">
            <span class="close-profile-modal" id="close-profile-modal-btn">&times;</span>
            <h2>My Profile</h2>
            <div id="modal-profile-photo-container">
                 <!-- SVG Placeholder for Profile Photo -->
                <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="50" fill="#ddd"/><path d="M50 62.5C40.075 62.5 31.25 68.125 31.25 75V81.25H68.75V75C68.75 68.125 59.925 62.5 50 62.5ZM50 25C55.175 25 59.375 29.2 59.375 34.375C59.375 39.55 55.175 43.75 50 43.75C44.825 43.75 40.625 39.55 40.625 34.375C40.625 29.2 44.825 25 50 25Z" fill="#aaa"/></svg>
            </div>
            <div class="profile-modal-details">
                <p><strong>Full Name:</strong> <span id="modal-profile-name">N/A</span></p>
                <p><strong>Email:</strong> <span id="modal-profile-email">N/A</span></p>
                <p><strong>Phone:</strong> <span id="modal-profile-phone">N/A</span></p>
                <p><strong>Gender:</strong> <span id="modal-profile-gender">N/A</span></p>
                <p><strong>Date of Birth:</strong> <span id="modal-profile-dob">N/A</span></p>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="new-main-header">
        <div class="user-info-line">
            <svg id="new-open-sidebar-btn" class="user-avatar-icon" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="50" fill="white"/><path d="M50 62.5C40.075 62.5 31.25 68.125 31.25 75V81.25H68.75V75C68.75 68.125 59.925 62.5 50 62.5ZM50 25C55.175 25 59.375 29.2 59.375 34.375C59.375 39.55 55.175 43.75 50 43.75C44.825 43.75 40.625 39.55 40.625 34.375C40.625 29.2 44.825 25 50 25Z" fill="#1e3a8a"/></svg>
            <span id="new-header-profile-name" class="user-name-display">Guest</span>
        </div>
        <div class="location-line" id="new-header-location-btn">
            <span class="location-pin-icon">📍</span>
            <span id="new-modern-location-text" class="location-address-text">Select Location</span>
            <span class="location-chevron-icon">▼</span>
        </div>
    </header>

    <!-- Location Selection Modal -->
    <div class="location-overlay" id="location-overlay">
        <div class="location-modal" id="location-modal">
            <span class="close-location-modal" id="close-location-modal-btn">&times;</span>
            <h2>Select Your Location</h2>
            <div id="map"></div>
            <p>Selected Address: <strong id="selected-address">Not set</strong></p>
            <input type="text" id="manual-address" placeholder="Or type address (e.g., Street, City)">
            <button id="use-current-location">Detect My Current Location</button>
            <button id="location-submit" disabled>Confirm Location</button>
            <p class="location-error" id="location-error"></p>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content-area">
        <div id="services-overview-section">
            <!-- NEW: Quick Actions Section -->
            <div id="quick-actions-container">
                <div class="quick-action-card" id="quick-action-doctor">
                    <img src="assets/img/doctor_icon_quick.png" alt="Doctor" class="quick-action-icon">
                    <span class="quick-action-text">Book a Doctor</span>
                    <span class="quick-action-arrow">&gt;</span>
                </div>
                <div class="quick-action-card" id="quick-action-nurse">
                    <img src="assets/img/nurse_icon_quick.png" alt="Nurse" class="quick-action-icon">
                    <span class="quick-action-text">Book a Nurse</span>
                    <span class="quick-action-arrow">&gt;</span>
                </div>
                <div class="quick-action-card" id="quick-action-home-service">
                    <img src="assets/img/homecare_icon_quick.png" alt="Home Service" class="quick-action-icon">
                    <span class="quick-action-text">Home Service</span>
                    <span class="quick-action-arrow">&gt;</span>
                </div>
            </div>

            <!-- MODIFIED: "Services" Title (was "Our Services") -->
            <h2 class="section-title" id="ourServicesTitle">Services</h2>

            <!-- MODIFIED: Detailed Service Cards Container (was modern-horizontal-cards, will be styled for vertical) -->
            <div class="modern-vertical-service-cards" id="ourServicesCards">
                <div class="modern-h-card" data-service-type="doctor">
                    <img src="assets/img/doctor_icon_detailed.png" alt="Doctor Icon" class="modern-h-card-img">
                    <div class="modern-h-card-content">
                        <span class="modern-h-card-title">Doctor appointment</span>
                        <ul class="modern-h-card-features">
                            <li>Certified doctors</li>
                            <li>Home visit available</li>
                            <li>Easy online booking</li>
                        </ul>
                    </div>
                </div>
                <div class="modern-h-card" data-service-type="nurse">
                    <img src="assets/img/nurse_icon_detailed.png" alt="Nurse Icon" class="modern-h-card-img">
                    <div class="modern-h-card-content">
                        <span class="modern-h-card-title">Nurse visit</span>
                        <ul class="modern-h-card-features">
                            <li>Professional nurses</li>
                            <li>24/7 support</li>
                            <li>Personalized care</li>
                        </ul>
                    </div>
                </div>
                <div class="modern-h-card" data-service-type="home-service">
                    <img src="assets/img/homecare_icon_detailed.png" alt="Home Care Icon" class="modern-h-card-img">
                    <div class="modern-h-card-content">
                        <span class="modern-h-card-title">Home Service</span>
                        <ul class="modern-h-card-features">
                            <li>Convenient at-home care</li>
                            <li>Flexible scheduling</li>
                            <li>Trusted by families</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- REMOVED "Meet Our Professionals" section from here -->

            <!-- EXISTING: FAQs Section -->
            <h2 class="section-title" id="faqTitle">Frequently Asked Questions</h2>
            <div class="faq-list" id="faq-list">
                <!-- FAQs will be populated by JS -->
            </div>
        </div>

        <div id="health-issues-selection-section" style="display: none;">
            <button id="back-to-services-overview-btn" style="display: inline-block; margin-bottom: 20px; padding: 10px 18px; border-radius: 20px; border: 1px solid #1976D2; background-color: #fff; color: #1976D2; font-weight: 600; cursor: pointer; transition: all 0.2s ease;">&larr; Back to Services</button>
            <h2 class="section-title">Select a Common Health Issue</h2>
            <div class="health-issues-grid">
                <div class="health-issue-item" data-issue="High BP">
                    <div class="health-issue-icon-placeholder">BP</div>
                    <span>High BP</span>
                </div>
                <div class="health-issue-item" data-issue="Diabetes">
                    <div class="health-issue-icon-placeholder">DB</div>
                    <span>Diabetes</span>
                </div>
                <div class="health-issue-item" data-issue="Covid-19">
                    <div class="health-issue-icon-placeholder">CV</div>
                    <span>Covid-19</span>
                </div>
                <div class="health-issue-item" data-issue="Fever/Flu">
                    <div class="health-issue-icon-placeholder">FG</div>
                    <span>Fever/Flu</span>
                </div>
                <div class="health-issue-item" data-issue="Joint Pain">
                    <div class="health-issue-icon-placeholder">JP</div>
                    <span>Joint Pain</span>
                </div>
                <div class="health-issue-item" data-issue="Headache">
                    <div class="health-issue-icon-placeholder">HA</div>
                    <span>Headache</span>
                </div>
            </div>
        </div>

        <div id="provider-list-section" style="display: none;">
            <button id="back-to-services-btn">&larr; Back to Services</button>
            <h2 id="provider-list-title">Available Providers</h2>
            <div id="provider-list-container">
                <!-- Provider cards will be populated by JS -->
            </div>
        </div>

        <div id="health-issue-patient-form-section" style="display:none; padding: 15px; background-color: #fff; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05); margin-top:10px;">
            <button id="back-to-overview-from-patient-form-btn">&larr; Back to Overview</button>
            <h2 id="patient-form-title" style="text-align:center; color:#223a7a; margin-bottom:20px;">Patient Details for <span id="selected-health-issue-name">Issue</span></h2>
            <form id="patient-details-health-issue-form">
                <div class="form-group">
                    <label for="patient-name-health-form">Full Name:</label>
                    <input type="text" id="patient-name-health-form" name="patientName" required>
                </div>
                <div class="form-group">
                    <label for="patient-age-health-form">Age:</label>
                    <input type="number" id="patient-age-health-form" name="patientAge" required>
                </div>
                <div class="form-group">
                    <label for="patient-gender-health-form">Gender:</label>
                    <select id="patient-gender-health-form" name="patientGender" required>
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="contact-number-health-form">Contact Number:</label>
                    <input type="tel" id="contact-number-health-form" name="contactNumber" required>
                </div>
                <div class="form-group">
                    <label for="address-health-form">Address:</label>
                    <textarea id="address-health-form" name="address" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="symptoms-health-form">Symptoms:</label>
                    <textarea id="symptoms-health-form" name="symptoms" rows="3" required></textarea>
                </div>
                <button type="submit" id="submit-patient-details-health-form-btn">Submit Details</button>
            </form>
        </div>

        <!-- Placeholder for other sections if needed -->
        <div id="my-bookings-content" style="display:none;"> <h2 class="section-title">My Bookings</h2> <p>Booking history will appear here.</p> </div>
        <div id="terms-conditions-content" style="display:none;"> <h2 class="section-title">Terms & Conditions</h2> <p>Terms and conditions content...</p> </div>
        <div id="contact-us-content" style="display:none;"> <h2 class="section-title">Contact Us</h2> <p>Contact information...</p> </div>

    </main>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.9.0/firebase-app.js";
        import { getAuth, onAuthStateChanged, signOut } from "https://www.gstatic.com/firebasejs/10.9.0/firebase-auth.js";
        import { getFirestore, doc, getDoc } from "https://www.gstatic.com/firebasejs/10.9.0/firebase-firestore.js";

        const firebaseConfig = {
            apiKey: "AIzaSyC62wo52XfJDOBnJc6VAQDDbse3a-KKy-k",
            authDomain: "dhipycare.firebaseapp.com",
            projectId: "dhipycare",
            storageBucket: "dhipycare.appspot.com",
            messagingSenderId: "493427173597",
            appId: "1:493427173597:web:379ad40ef8360df81ad334",
            measurementId: "G-3S582507X2"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // DOM Elements
        const sidebar = document.getElementById('sidebar');
        const openSidebarBtn = document.getElementById('new-open-sidebar-btn'); // UPDATED ID
        const closeSidebarBtn = document.getElementById('close-sidebar-btn');
        const sidebarOverlay = document.getElementById('sidebar-overlay');

        const headerProfileName = document.getElementById('new-header-profile-name'); // UPDATED ID
        const sidebarProfilePhotoContainer = document.getElementById('sidebar-profile-photo-container');
        const sidebarProfileName = document.getElementById('sidebar-profile-name');

        const profileModal = document.getElementById('profile-modal');
        const myAccountBtn = document.getElementById('my-account-btn');
        const closeProfileModalBtn = document.getElementById('close-profile-modal-btn');

        const modalProfilePhotoContainer = document.getElementById('modal-profile-photo-container');
        const modalProfileName = document.getElementById('modal-profile-name');
        const modalProfileEmail = document.getElementById('modal-profile-email');
        const modalProfilePhone = document.getElementById('modal-profile-phone');
        const modalProfileGender = document.getElementById('modal-profile-gender');
        const modalProfileDob = document.getElementById('modal-profile-dob');

        const logoutBtn = document.getElementById('logout-btn');

        const locationOverlay = document.getElementById('location-overlay');
        const locationModal = document.getElementById('location-modal');
        const headerLocationBtn = document.getElementById('new-header-location-btn'); // UPDATED ID
        const closeLocationModalBtn = document.getElementById('close-location-modal-btn');
        const modernLocationText = document.getElementById('new-modern-location-text'); // UPDATED ID
        const mapElement = document.getElementById('map');
        const manualAddressInput = document.getElementById('manual-address');
        const useCurrentLocationBtn = document.getElementById('use-current-location');
        const locationSubmitBtn = document.getElementById('location-submit');
        const selectedAddressDisplay = document.getElementById('selected-address');
        const locationError = document.getElementById('location-error');

        const servicesOverviewSection = document.getElementById('services-overview-section');
        const providerListSection = document.getElementById('provider-list-section');
        const backToServicesBtn = document.getElementById('back-to-services-btn');
        const providerListTitle = document.getElementById('provider-list-title');
        const providerListContainer = document.getElementById('provider-list-container');

        const myBookingsBtn = document.getElementById('my-bookings-btn');
        const termsConditionsBtn = document.getElementById('terms-conditions-btn');
        const contactUsBtn = document.getElementById('contact-us-btn');

        const myBookingsContent = document.getElementById('my-bookings-content');
        const termsConditionsContent = document.getElementById('terms-conditions-content');
        const contactUsContent = document.getElementById('contact-us-content');

        const healthIssuesSelectionSection = document.getElementById('health-issues-selection-section');
        const backToServicesOverviewBtn = document.getElementById('back-to-services-overview-btn');
        const healthIssuePatientFormSection = document.getElementById('health-issue-patient-form-section');
        const backToOverviewFromPatientFormBtn = document.getElementById('back-to-overview-from-patient-form-btn');
        const patientDetailsHealthIssueForm = document.getElementById('patient-details-health-issue-form');
        const selectedHealthIssueNameSpan = document.getElementById('selected-health-issue-name');
        const addressHealthFormTextarea = document.getElementById('address-health-form');

        // --- More granular DOM Elements for section control ---
        const quickActionsContainer = document.getElementById('quick-actions-container'); // NEW
        const ourServicesTitle = document.getElementById('ourServicesTitle'); // ID kept from previous structure
        const ourServicesCards = document.getElementById('ourServicesCards'); // ID kept, now modern-vertical-service-cards
        // const meetProfessionalsTitle = Array.from(document.querySelectorAll('#services-overview-section > .section-title')).find(el => el.textContent.trim().startsWith('Meet Our Professionals')); // REMOVED
        // const meetProfessionalsScroller = document.getElementById('doctor-scroller')?.closest('.scrolling-wrapper-container'); // REMOVED
        const faqMainTitle = document.getElementById('faqTitle'); // ID kept for FAQ title
        const faqList = document.getElementById('faq-list');
        const healthIssuesListTitle = document.querySelector('#health-issues-selection-section > h2.section-title');


        let map;
        let currentMarker;
        let selectedLatLng = null;
        let selectedServiceType = null;
        let initialLocationSet = false; // Flag to track if location has been set/loaded

        // --- Initial console logs for element check ---
        console.log("Initial check: servicesOverviewSection:", document.getElementById('services-overview-section'));
        console.log("Initial check: healthIssuesSelectionSection:", document.getElementById('health-issues-selection-section'));

        // --- Authentication Listener ---
        onAuthStateChanged(auth, (user) => {
            if (user) {
                console.log("User is logged in:", user.uid);
                fetchAndDisplayUserProfile(user.uid);
            } else {
                console.log("User is logged out.");
                // Update new header elements for guest
                if (headerProfileName) headerProfileName.textContent = 'Guest';
                // setDefaultProfilePhotoForAvatarIcon(document.getElementById('new-open-sidebar-btn')); // Optional: if icon needs initials

                sidebarProfileName.textContent = 'Guest User';
                setDefaultProfilePhoto(sidebarProfilePhotoContainer);
                setDefaultProfilePhoto(modalProfilePhotoContainer);
            }
        });

        // --- User Profile Functions ---
        async function fetchAndDisplayUserProfile(userId) {
            try {
                const userDocRef = doc(db, "users", userId);
                const userDocSnap = await getDoc(userDocRef);

                if (userDocSnap.exists()) {
                    const userData = userDocSnap.data();
                    console.log("User data from Firestore:", userData);

                    // Update New Header
                    if (headerProfileName) headerProfileName.textContent = userData.fullName || userData.displayName || 'User';
                    // Update avatar icon if it displays photo/initials, for now it's a static SVG, but could be like sidebarProfilePhotoContainer
                    // updateProfilePhoto(document.getElementById('new-open-sidebar-btn'), userData.photoURL, userData.fullName);

                    // Update Sidebar Profile Summary
                    if (sidebarProfileName) sidebarProfileName.textContent = userData.fullName || 'User Name';
                    updateProfilePhoto(sidebarProfilePhotoContainer, userData.photoURL, userData.fullName);


                    // Update Profile Modal
                    if (modalProfileName) modalProfileName.textContent = userData.fullName || 'N/A';
                    if (modalProfileEmail) modalProfileEmail.textContent = userData.email || 'N/A';
                    if (modalProfilePhone) modalProfilePhone.textContent = userData.phoneNumber || 'N/A';
                    if (modalProfileGender) modalProfileGender.textContent = userData.gender || 'N/A';
                    if (modalProfileDob) modalProfileDob.textContent = userData.dob || 'N/A';
                    updateProfilePhoto(modalProfilePhotoContainer, userData.photoURL, userData.fullName);

                } else {
                    console.log("No such user document in Firestore!");
                    // Fallback to auth data if Firestore doc doesn't exist
                    const currentUser = auth.currentUser;
                    if (currentUser) {
                         if (headerProfileName) headerProfileName.textContent = currentUser.displayName || currentUser.phoneNumber || 'User'; // Update new header
                         // updateProfilePhoto(document.getElementById('new-open-sidebar-btn'), currentUser.photoURL, currentUser.displayName);

                         if (sidebarProfileName) sidebarProfileName.textContent = currentUser.displayName || 'User Name';
                         updateProfilePhoto(sidebarProfilePhotoContainer, currentUser.photoURL, currentUser.displayName);

                         if (modalProfileName) modalProfileName.textContent = currentUser.displayName || 'N/A';
                         if (modalProfileEmail) modalProfileEmail.textContent = currentUser.email || 'N/A';
                         if (modalProfilePhone) modalProfilePhone.textContent = currentUser.phoneNumber || 'N/A';
                         updateProfilePhoto(modalProfilePhotoContainer, currentUser.photoURL, currentUser.displayName);
                    }
                }
            } catch (error) {
                console.error("Error fetching user profile:", error);
            }
        }

        function updateProfilePhoto(containerElement, photoURL, fullName) {
            if (!containerElement) return;
            containerElement.innerHTML = ''; // Clear previous photo/placeholder
            if (photoURL) {
                const img = document.createElement('img');
                img.src = photoURL;
                img.alt = fullName || 'Profile Photo';
                img.onerror = () => setDefaultProfilePhoto(containerElement, fullName); // Fallback on error
                containerElement.appendChild(img);
            } else {
                setDefaultProfilePhoto(containerElement, fullName);
            }
        }

        function setDefaultProfilePhoto(containerElement, fullName) {
            if (!containerElement) return;
            containerElement.innerHTML = ''; // Clear previous
            const initial = fullName ? fullName.charAt(0).toUpperCase() : '?';
            const svgPlaceholder = `
                <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="50" r="50" fill="#e0e0e0"/>
                    <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-family="Arial, sans-serif" font-size="50px" fill="#555">${initial}</text>
                </svg>`;
            containerElement.innerHTML = svgPlaceholder;
        }


        // --- Sidebar Functionality ---
        if (openSidebarBtn) {
            openSidebarBtn.addEventListener('click', () => {
                console.log("Opening sidebar with new button");
                sidebar.classList.add('open');
                sidebarOverlay.classList.add('active');
            });
        }
        if (closeSidebarBtn) {
            closeSidebarBtn.addEventListener('click', closeSidebar);
        }
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', closeSidebar);
        }
        function closeSidebar() {
            sidebar.classList.remove('open');
            sidebarOverlay.classList.remove('active');
            // Do not automatically show servicesOverviewSection here, let specific actions control content
        }

        // New function to hide major page sections, leaving overview and health issues to be managed specifically
        function hideAllMajorPageSections() {
            console.log("[Debug] hideAllMajorPageSections called.");
            if (providerListSection) providerListSection.style.display = 'none';
            if (myBookingsContent) myBookingsContent.style.display = 'none';
            if (termsConditionsContent) termsConditionsContent.style.display = 'none';
            if (contactUsContent) contactUsContent.style.display = 'none';
            if (healthIssuePatientFormSection) healthIssuePatientFormSection.style.display = 'none';
            // This function explicitly does NOT hide servicesOverviewSection or healthIssuesSelectionSection
        }

        function showFullServicesOverview() {
            hideAllMajorPageSections(); // Hide other main content like forms, provider lists, etc.

            if (healthIssuesSelectionSection) healthIssuesSelectionSection.style.display = 'none';
            if (servicesOverviewSection) servicesOverviewSection.style.display = 'block';

            // Ensure all parts of the NEW services overview are visible
            if (quickActionsContainer) quickActionsContainer.style.display = 'flex'; // Show quick actions
            if (ourServicesTitle) ourServicesTitle.style.display = 'block'; // Show "Services" title
            if (ourServicesCards) ourServicesCards.style.display = 'flex'; // Show detailed service cards (flex column)

            // Ensure "Meet Our Professionals" is NOT shown (it's removed from HTML, but good to be safe if elements were just hidden)
            // if (meetProfessionalsTitle) meetProfessionalsTitle.style.display = 'none';
            // if (meetProfessionalsScroller) meetProfessionalsScroller.style.display = 'none';

            if (faqMainTitle) faqMainTitle.style.display = 'block'; // Show FAQ Title
            if (faqList) faqList.style.display = 'block'; // Show FAQ List
            console.log("[Debug] showFullServicesOverview executed with new layout.");
        }

        function showSection(sectionElement) {
            // This function is used by sidebar navigation.
            hideAllMajorPageSections(); // Hide other forms/lists
            if (servicesOverviewSection) servicesOverviewSection.style.display = 'none'; // Hide full overview
            if (healthIssuesSelectionSection) healthIssuesSelectionSection.style.display = 'none'; // Hide health issues
            if (sectionElement) sectionElement.style.display = 'block';
            closeSidebar();
            console.log(`[Debug] showSection executed for: ${sectionElement ? sectionElement.id : 'null'}`);
        }

        // --- Profile Modal Functionality ---
        if (myAccountBtn) {
            myAccountBtn.addEventListener('click', () => {
                profileModal.style.display = 'flex'; // Use flex for centering
                closeSidebar(); // Close sidebar when modal opens
            });
        }
        if (closeProfileModalBtn) {
            closeProfileModalBtn.addEventListener('click', () => {
                profileModal.style.display = 'none';
            });
        }
        window.addEventListener('click', (event) => {
            if (event.target == profileModal) {
                profileModal.style.display = 'none';
            }
            if (event.target == locationOverlay) {
                 if (initialLocationSet) { // Only allow close if location has been set/loaded
                    closeLocationModal();
                } else {
                    locationError.textContent = 'Please select and confirm a location to proceed.';
                }
            }
        });

        // --- Logout ---
        if (logoutBtn) {
            logoutBtn.addEventListener('click', async () => {
                try {
                    await signOut(auth);
                    console.log("User signed out successfully.");
                    window.location.href = 'index_login.html'; // Redirect to login page
                } catch (error) {
                    console.error("Error signing out:", error);
                }
            });
        }

        // --- Sidebar Navigation ---
        if (myBookingsBtn && myBookingsContent) myBookingsBtn.onclick = () => showSection(myBookingsContent);
        if (termsConditionsBtn && termsConditionsContent) termsConditionsBtn.onclick = () => showSection(termsConditionsContent);
        if (contactUsBtn && contactUsContent) contactUsBtn.onclick = () => showSection(contactUsContent);


        // --- Location Modal and Map Functionality ---
        function initMap() {
            if (mapElement && !map) {
                map = L.map(mapElement).setView([20.5937, 78.9629], 5);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(map);
                map.on('click', function(e) {
                    setLatLng(e.latlng);
                    fetchAddress(e.latlng);
                });
            }
        }

        function setLatLng(latlng) {
            selectedLatLng = latlng;
            if (currentMarker) {
                currentMarker.setLatLng(latlng);
            } else {
                currentMarker = L.marker(latlng).addTo(map);
            }
            map.panTo(latlng);
            locationSubmitBtn.disabled = false;
        }

        async function fetchAddress(latlng) {
            try {
                const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latlng.lat}&lon=${latlng.lng}`);
                if (!response.ok) throw new Error('Failed to fetch address');
                const data = await response.json();
                if (data && data.display_name) {
                    selectedAddressDisplay.textContent = data.display_name;
                    manualAddressInput.value = data.display_name;
                    locationError.textContent = '';
                } else {
                    selectedAddressDisplay.textContent = 'Address not found.';
                }
            } catch (error) {
                console.error("Error fetching address:", error);
                selectedAddressDisplay.textContent = 'Could not fetch address.';
                locationError.textContent = 'Error fetching address. Try manual input.';
            }
        }

        function updateHeaderLocation(text) {
            if (modernLocationText) {
                // Ensure the text fits within the new layout constraints
                const maxLength = 30; // Max characters for location text in new header
                modernLocationText.textContent = text.length > maxLength ? text.substring(0, maxLength - 3) + '...' : text;
            }
        }

        if (headerLocationBtn) {
            headerLocationBtn.addEventListener('click', () => {
                console.log("Opening location modal with new button");
                locationOverlay.classList.add('active');
                setTimeout(initMap, 100);
            });
        }

        function closeLocationModal() {
            locationOverlay.classList.remove('active');
            locationError.textContent = '';
        }

        if (closeLocationModalBtn) {
            closeLocationModalBtn.addEventListener('click', () => {
                if (initialLocationSet) { // Only allow close if location has been set/loaded
                    closeLocationModal();
                } else {
                    locationError.textContent = 'Please select and confirm a location to continue.';
                }
            });
        }

        if (useCurrentLocationBtn) {
            useCurrentLocationBtn.addEventListener('click', () => {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(position => {
                        const latlng = L.latLng(position.coords.latitude, position.coords.longitude);
                        setLatLng(latlng);
                        fetchAddress(latlng);
                        map.setView(latlng, 13);
                    }, error => {
                        console.error("Error getting current location:", error);
                        locationError.textContent = 'Could not get current location. Please enable location services or enter manually.';
                    });
                } else {
                    locationError.textContent = 'Geolocation is not supported by your browser.';
                }
            });
        }

        if (locationSubmitBtn) {
            locationSubmitBtn.addEventListener('click', () => {
                let locationToSet = "Location Not Set";
                if (selectedLatLng && selectedAddressDisplay.textContent !== 'Address not found.' && selectedAddressDisplay.textContent !== 'Could not fetch address.' && selectedAddressDisplay.textContent !== 'Not set') {
                     locationToSet = selectedAddressDisplay.textContent;
                } else if (manualAddressInput.value.trim() !== "") {
                     locationToSet = manualAddressInput.value.trim();
                }

                if (locationToSet !== "Location Not Set") {
                    updateHeaderLocation(locationToSet);
                    localStorage.setItem('userLocation', locationToSet);
                    if(selectedLatLng) localStorage.setItem('userLatLng', JSON.stringify(selectedLatLng));

                    const justSetInitial = !initialLocationSet; // Check if this was the first time
                    initialLocationSet = true; // Mark it as set now

                    closeLocationModal();

                    if (justSetInitial) { // If this was the initial mandatory selection
                        showFullServicesOverview(); // Now display the main page content
                        console.log("Initial location set successfully. Displaying main content.");
                    }
                } else {
                    locationError.textContent = 'Please select a location on the map or enter an address.';
                }
            });
        }

        // const savedLocation = localStorage.getItem('userLocation'); // Moved to initial load logic
        // if (savedLocation) { ... } // Moved to initial load logic


        // --- Service Card Click & Provider List Functionality ---
        const serviceCards = document.querySelectorAll('.modern-h-card');
        serviceCards.forEach(card => {
            card.addEventListener('click', () => {
                console.log("[Debug] Service card clicked. Card:", card);
                selectedServiceType = card.dataset.serviceType;
                console.log("[Debug] Selected service type:", selectedServiceType);

                hideAllMajorPageSections(); // Start by hiding common sections like forms, provider lists

                if (selectedServiceType === 'doctor') {
                    if (servicesOverviewSection) servicesOverviewSection.style.display = 'block';
                    if (healthIssuesSelectionSection) healthIssuesSelectionSection.style.display = 'block';

                    // Configure servicesOverviewSection for doctor view (professionals only)
                    if (ourServicesTitle) ourServicesTitle.style.display = 'none';
                    if (ourServicesCards) ourServicesCards.style.display = 'none';
                    if (meetProfessionalsTitle) meetProfessionalsTitle.style.display = 'block';
                    if (meetProfessionalsScroller) meetProfessionalsScroller.style.display = 'block';
                    if (faqTitle) faqTitle.style.display = 'none';
                    if (faqList) faqList.style.display = 'none';

                    if (healthIssuesListTitle) healthIssuesListTitle.textContent = 'What are you concerned about?';
                    console.log("[Debug] Doctor service selected. Showing professionals + health issues grid.");

                } else { // For 'nurse', 'home-service', or other types
                    if (servicesOverviewSection) servicesOverviewSection.style.display = 'none';
                    if (healthIssuesSelectionSection) healthIssuesSelectionSection.style.display = 'none';

                    if (providerListSection) {
                        renderProviderList(selectedServiceType);
                        providerListSection.style.display = 'block';
                        console.log(`[Debug] Showing providerListSection for ${selectedServiceType} service.`);
                    } else {
                        console.error("[Error] providerListSection is null! Cannot show it.");
                    }
                }
            });
        });

        if (backToServicesBtn) { // This is from provider-list-section
            backToServicesBtn.addEventListener('click', () => {
                console.log("[Debug] 'Back to Services' (from provider list) clicked.");
                showFullServicesOverview();
            });
        }

        if (backToServicesOverviewBtn) { // This is from health-issues-selection-section
            backToServicesOverviewBtn.addEventListener('click', () => {
                console.log("[Debug] 'Back to Services Overview' (from health issues) clicked.");
                showFullServicesOverview();
                selectedServiceType = null; // Reset service type
            });
        }

        function renderProviderList(type) {
            providerListContainer.innerHTML = '';
            let providers = [];
            let title = "Available ";

            if (type === 'doctor') {
                title += "Doctors";
                providers = [
                    { name: "Dr. Smith", specialty: "Cardiologist", details: "10 years experience, MD", img: "assets/img/doc1.png" },
                    { name: "Dr. Jones", specialty: "Pediatrician", details: "Friendly and great with kids", img: "assets/img/doc2.png" },
                    { name: "Dr. Lee", specialty: "General Physician", details: "Available for video consult", img: "assets/img/doc3.png" },
                ];
            } else if (type === 'nurse') {
                title += "Nurses";
                providers = [
                    { name: "Nurse Anna", specialty: "Registered Nurse", details: "5 years in home care", img: "assets/img/nurse1.png" },
                    { name: "Nurse Ben", specialty: "Cardiac Care Nurse", details: "Specializes in post-op care", img: "assets/img/nurse2.png" },
                ];
            } else if (type === 'home-service') {
                title += "Home Health Aides";
                 providers = [
                    { name: "Aide Maria", specialty: "Elderly Care", details: "Certified HHA, compassionate", img: "assets/img/aide1.png" },
                    { name: "Aide David", specialty: "Physical Therapy Asst.", details: "Helps with mobility exercises", img: "assets/img/aide2.png" },
                ];
            }
            providerListTitle.textContent = title;

            if (providers.length === 0) {
                providerListContainer.innerHTML = '<p>No providers available for this service at the moment.</p>';
                return;
            }

            providers.forEach(provider => {
                const cardDiv = document.createElement('div');
                cardDiv.className = 'provider-card';
                cardDiv.innerHTML = `
                    <div class="provider-card-header">
                        <img src="${provider.img || 'assets/img/default_provider.png'}" alt="${provider.name}" class="provider-card-img">
                        <div class="provider-card-info">
                            <h4>${provider.name}</h4>
                            <p>${provider.specialty}</p>
                        </div>
                    </div>
                    <p class="provider-card-details">${provider.details}</p>
                    <button class="provider-select-btn">Select ${provider.name}</button>
                `;
                cardDiv.querySelector('.provider-select-btn').addEventListener('click', () => {
                    alert(`Selected ${provider.name}. Next step: booking/confirmation.`);
                });
                providerListContainer.appendChild(cardDiv);
            });
        }

        // --- FAQ Functionality ---
        const faqData = [
            { q: "How do I book an appointment?", a: "You can book an appointment by selecting a service, choosing a provider, and then confirming your preferred time slot. Payment can be made online." },
            { q: "What are the consultation charges?", a: "Consultation charges vary by doctor and service. Please check the provider's profile for specific fees." },
            { q: "Can I reschedule my appointment?", a: "Yes, you can reschedule your appointment up to 24 hours before the scheduled time through your 'My Bookings' section." },
            { q: "Is online payment secure?", a: "Yes, we use industry-standard encryption and secure payment gateways to protect your transaction details." }
        ];

        if (faqList) {
            faqData.forEach(item => {
                const faqItemDiv = document.createElement('div');
                faqItemDiv.classList.add('faq-item');
                faqItemDiv.innerHTML = `
                    <button class="faq-question">${item.q}</button>
                    <div class="faq-answer"><p>${item.a}</p></div>
                `;
                faqList.appendChild(faqItemDiv);
            });

            const faqItems = document.querySelectorAll('.faq-item');
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question');
                question.addEventListener('click', () => {
                    faqItems.forEach(otherItem => {
                        if (otherItem !== item && otherItem.classList.contains('active')) {
                            otherItem.classList.remove('active');
                        }
                    });
                    item.classList.toggle('active');
                });
            });
        }

        // --- Populate Doctor/Nurse Scrollers (Sample Data) ---
        function populateScroller(scrollerId, items, type) {
            const scroller = document.getElementById(scrollerId);
            if (!scroller) return;
            scroller.innerHTML = '';
            items.forEach(item => {
                const div = document.createElement('div');
                div.className = `${type}-scroll-item`;
                div.innerHTML = `
                    <img src="${item.img}" alt="${item.name}">
                    <span class="${type}-name">${item.name}</span>
                    <span class="${type}-description">${item.desc}</span>
                `;
                scroller.appendChild(div);
            });
        }

        const sampleDoctors = [
            { name: "Dr. Anya Sharma", desc: "Cardiologist", img: "assets/img/doc1.png" },
            { name: "Dr. Ben Carter", desc: "Pediatrician", img: "assets/img/doc2.png" },
            { name: "Dr. Chloe Davis", desc: "Dermatologist", img: "assets/img/doc3.png" },
            { name: "Dr. Dev Patel", desc: "Neurologist", img: "assets/img/doc4.png" }
        ];
        populateScroller('doctor-scroller', sampleDoctors, 'doctor');

        const healthIssueItems = document.querySelectorAll('.health-issue-item');
        healthIssueItems.forEach(item => {
            item.addEventListener('click', () => {
                const issueName = item.dataset.issue || (item.querySelector('span') ? item.querySelector('span').textContent : 'Selected Issue');
                console.log(`[Debug] Health issue item clicked: ${issueName}`);

                if (!selectedServiceType || selectedServiceType !== 'doctor') {
                    console.warn("[Warning] Health issue clicked, but serviceType is not 'doctor' or not selected.", selectedServiceType);
                    // Optionally, redirect back to full services overview if state is unexpected
                    // showFullServicesOverview();
                    return;
                }

                // servicesOverviewSection (with professionals) should remain visible.
                // Hide the health issues grid and show the patient form.
                if (healthIssuesSelectionSection) healthIssuesSelectionSection.style.display = 'none';
                if (healthIssuePatientFormSection) healthIssuePatientFormSection.style.display = 'block';

                if (selectedHealthIssueNameSpan) {
                     selectedHealthIssueNameSpan.textContent = `${issueName}`; // Simpler name for the form
                }
                if (document.getElementById('patient-form-title')) {
                    document.getElementById('patient-form-title').innerHTML = `Patient Details for <span id="selected-health-issue-name">${issueName}</span>`;
                }

                const savedLocation = localStorage.getItem('userLocation');
                if (patientDetailsHealthIssueForm) patientDetailsHealthIssueForm.reset();
                if (savedLocation && addressHealthFormTextarea) {
                    addressHealthFormTextarea.value = savedLocation;
                }
                console.log("[Debug] Displaying patient form for health issue.");
            });
        });

        if (backToOverviewFromPatientFormBtn) {
            backToOverviewFromPatientFormBtn.addEventListener('click', () => {
                console.log("[Debug] 'Back to Overview from Patient Form' clicked.");
                if (healthIssuePatientFormSection) healthIssuePatientFormSection.style.display = 'none';

                // Restore the "doctor" specific view (professionals + health issues grid)
                if (selectedServiceType === 'doctor') {
                    if (servicesOverviewSection) servicesOverviewSection.style.display = 'block'; // Should be visible
                    if (healthIssuesSelectionSection) healthIssuesSelectionSection.style.display = 'block'; // Show grid

                    // Ensure servicesOverviewSection parts are correct for doctor view
                    if (ourServicesTitle) ourServicesTitle.style.display = 'none';
                    if (ourServicesCards) ourServicesCards.style.display = 'none';
                    if (meetProfessionalsTitle) meetProfessionalsTitle.style.display = 'block';
                    if (meetProfessionalsScroller) meetProfessionalsScroller.style.display = 'block';
                    if (faqTitle) faqTitle.style.display = 'none';
                    if (faqList) faqList.style.display = 'none';
                    if (healthIssuesListTitle) healthIssuesListTitle.textContent = 'What are you concerned about?';
                    console.log("[Debug] Restored doctor view (professionals + health issues grid).");
                } else {
                    // Fallback, though ideally serviceType is still 'doctor'
                    showFullServicesOverview();
                     console.warn("[Warning] Back from patient form, but service type was not doctor. Showing full overview.");
                }
            });
        }

        if (patientDetailsHealthIssueForm) {
            patientDetailsHealthIssueForm.addEventListener('submit', (event) => {
                event.preventDefault();
                const formData = new FormData(patientDetailsHealthIssueForm);
                const patientDetails = {};
                formData.forEach((value, key) => {
                    patientDetails[key] = value;
                });
                patientDetails['serviceType'] = selectedServiceType; // Add service type to form data
                patientDetails['healthIssue'] = document.getElementById('selected-health-issue-name').textContent.split(' (Service:')[0]; // Get clean issue name

                console.log("Patient Details for Health Issue Submitted:", patientDetails);
                alert("Details submitted (see console for data). Booking flow to be implemented.");
                // Potentially hide form and show overview, or a confirmation message
                // healthIssuePatientFormSection.style.display = 'none';
                // servicesOverviewSection.style.display = 'block';
                // selectedServiceType = null; // Reset after submission
            });
        }

        console.log("Home page script loaded and initialized. All initial checks and listeners should be set up.");

        // Ensure the services overview is visible by default if no other section is active
        // Replace the old logic with a call to showFullServicesOverview() for initial setup
        const savedLocationOnLoad = localStorage.getItem('userLocation');
        if (savedLocationOnLoad) {
            updateHeaderLocation(savedLocationOnLoad);
            if (selectedAddressDisplay) selectedAddressDisplay.textContent = savedLocationOnLoad;
            if (manualAddressInput) manualAddressInput.value = savedLocationOnLoad;
            if (locationSubmitBtn) locationSubmitBtn.disabled = false;
            const savedLatLngOnLoad = localStorage.getItem('userLatLng');
            if (savedLatLngOnLoad) {
                selectedLatLng = JSON.parse(savedLatLngOnLoad);
            }
            initialLocationSet = true; // Mark that location was already set
            showFullServicesOverview(); // Load main page
            console.log("Page load: Location found in localStorage, displaying main content.");
        } else {
            // No location saved, force modal
            if (locationOverlay) locationOverlay.classList.add('active');
            setTimeout(initMap, 100); // Initialize map for the modal
            console.log("Page load: No location found, showing location modal mandatorily.");
        }

        // --- Service Card Click & Provider List Functionality (for DETAILED service cards) ---
        const detailedServiceCards = document.querySelectorAll('#ourServicesCards .modern-h-card'); // Target detailed cards
        detailedServiceCards.forEach(card => {
            card.addEventListener('click', () => {
                console.log("[Debug] DETAILED Service card clicked. Card:", card);
                selectedServiceType = card.dataset.serviceType;
                console.log("[Debug] Selected service type:", selectedServiceType);

                hideAllMajorPageSections();
                if (servicesOverviewSection) servicesOverviewSection.style.display = 'none'; // Hide the main overview

                if (selectedServiceType === 'doctor') {
                    if (healthIssuesSelectionSection) healthIssuesSelectionSection.style.display = 'block';
                    if (healthIssuesListTitle) healthIssuesListTitle.textContent = 'What are you concerned about?';
                    console.log("[Debug] Doctor service selected from detailed card. Showing health issues grid.");
                } else { // For 'nurse', 'home-service', or other types
                    if (providerListSection) {
                        renderProviderList(selectedServiceType);
                        providerListSection.style.display = 'block';
                        console.log(`[Debug] Showing providerListSection for ${selectedServiceType} service from detailed card.`);
                    } else {
                        console.error("[Error] providerListSection is null! Cannot show it.");
                    }
                }
            });
        });

        // --- NEW: Quick Action Card Click Handlers ---
        const quickActionDoctor = document.getElementById('quick-action-doctor');
        const quickActionNurse = document.getElementById('quick-action-nurse');
        const quickActionHomeService = document.getElementById('quick-action-home-service');

        if (quickActionDoctor) {
            quickActionDoctor.addEventListener('click', () => {
                console.log("[Debug] Quick Action: Book a Doctor clicked.");
                selectedServiceType = 'doctor'; // Set service type
                hideAllMajorPageSections();
                if (servicesOverviewSection) servicesOverviewSection.style.display = 'none';
                if (healthIssuesSelectionSection) healthIssuesSelectionSection.style.display = 'block';
                if (healthIssuesListTitle) healthIssuesListTitle.textContent = 'What are you concerned about?';
            });
        }

        if (quickActionNurse) {
            quickActionNurse.addEventListener('click', () => {
                console.log("[Debug] Quick Action: Book a Nurse clicked.");
                selectedServiceType = 'nurse'; // Set service type
                hideAllMajorPageSections();
                if (servicesOverviewSection) servicesOverviewSection.style.display = 'none';
                if (providerListSection) {
                    renderProviderList('nurse');
                    providerListSection.style.display = 'block';
                } else {
                    console.error("[Error] providerListSection is null! Cannot show it.");
                }
            });
        }

        if (quickActionHomeService) {
            quickActionHomeService.addEventListener('click', () => {
                console.log("[Debug] Quick Action: Home Service clicked.");
                selectedServiceType = 'home-service'; // Set service type
                hideAllMajorPageSections();
                if (servicesOverviewSection) servicesOverviewSection.style.display = 'none';
                if (providerListSection) {
                    renderProviderList('home-service');
                    providerListSection.style.display = 'block';
                } else {
                    console.error("[Error] providerListSection is null! Cannot show it.");
                }
            });
        }

        if (backToServicesBtn) { // This is from provider-list-section
            backToServicesBtn.addEventListener('click', () => {
                console.log("[Debug] 'Back to Services' (from provider list) clicked.");
                showFullServicesOverview();
            });
        }
    </script>

</body>
</html>
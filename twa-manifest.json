{"packageId": "app.web.dhipycare.twa", "host": "dhipycare.web.app", "name": "DhipyCar", "launcherName": "DhipyCare", "display": "standalone", "themeColor": "#000000", "themeColorDark": "#000000", "navigationColor": "#000000", "navigationColorDark": "#000000", "navigationDividerColor": "#000000", "navigationDividerColorDark": "#000000", "backgroundColor": "#FFFFFF", "enableNotifications": true, "startUrl": "/", "iconUrl": "https://dhipycare.web.app/assets/img/homescreenicon.png", "maskableIconUrl": "https://dhipycare.web.app/assets/img/homescreenicon.png", "splashScreenFadeOutDuration": 300, "signingKey": {"path": "C:\\DhipCare\\code\\dhipycar\\android.keystore", "alias": "android"}, "appVersionName": "4", "appVersionCode": 2, "shortcuts": [], "generatorApp": "bubblewrap-cli", "webManifestUrl": "https://dhipycare.web.app/manifest.json", "fallbackType": "customtabs", "features": {}, "alphaDependencies": {"enabled": false}, "enableSiteSettingsShortcut": true, "isChromeOSOnly": false, "isMetaQuest": false, "fullScopeUrl": "https://dhipycare.web.app/", "minSdkVersion": 21, "orientation": "portrait", "fingerprints": [], "additionalTrustedOrigins": [], "retainedBundles": [], "protocolHandlers": [], "appVersion": "4"}